import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Eye, 
  Lock, 
  Users, 
  Globe, 
  FileText,
  Calendar,
  Mail,
  Phone,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { seoConfig } from '../lib/seo-config';

export function PrivacyPolicyPage() {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const sections = [
    {
      id: 'information-collection',
      title: 'Information We Collect',
      icon: Eye,
      content: [
        {
          subtitle: 'Account Information',
          details: 'When you create an account, we collect your email address, name, and company information (if provided). This information is necessary to provide our services and communicate with you.'
        },
        {
          subtitle: 'App Review Data',
          details: 'We collect and analyze publicly available app reviews from various platforms (App Store, Google Play, etc.) that you choose to analyze. This data is processed to generate insights and reports.'
        },
        {
          subtitle: 'Usage Information',
          details: 'We automatically collect information about how you use our service, including pages visited, features used, and time spent on our platform. This helps us improve our service.'
        },
        {
          subtitle: 'Technical Information',
          details: 'We collect technical information such as IP address, browser type, device information, and operating system to ensure security and optimize performance.'
        }
      ]
    },
    {
      id: 'information-use',
      title: 'How We Use Your Information',
      icon: Users,
      content: [
        {
          subtitle: 'Service Provision',
          details: 'We use your information to provide, maintain, and improve our app review analysis services, including generating reports and insights.'
        },
        {
          subtitle: 'Communication',
          details: 'We may use your contact information to send you service-related notifications, updates, and marketing communications (with your consent).'
        },
        {
          subtitle: 'Analytics and Improvement',
          details: 'We analyze usage patterns to improve our service, develop new features, and enhance user experience.'
        },
        {
          subtitle: 'Security and Compliance',
          details: 'We use your information to detect and prevent fraud, ensure security, and comply with legal obligations.'
        }
      ]
    },
    {
      id: 'information-sharing',
      title: 'Information Sharing and Disclosure',
      icon: Globe,
      content: [
        {
          subtitle: 'Third-Party Service Providers',
          details: 'We may share your information with trusted third-party service providers who assist us in operating our service, such as cloud hosting, analytics, and payment processing.'
        },
        {
          subtitle: 'Legal Requirements',
          details: 'We may disclose your information if required by law, court order, or government request, or to protect our rights and safety.'
        },
        {
          subtitle: 'Business Transfers',
          details: 'In the event of a merger, acquisition, or sale of assets, your information may be transferred as part of the business transaction.'
        },
        {
          subtitle: 'Consent',
          details: 'We may share your information with your explicit consent for specific purposes not covered in this policy.'
        }
      ]
    },
    {
      id: 'data-security',
      title: 'Data Security',
      icon: Lock,
      content: [
        {
          subtitle: 'Encryption',
          details: 'All data transmission is encrypted using industry-standard SSL/TLS protocols. Data at rest is encrypted using AES-256 encryption.'
        },
        {
          subtitle: 'Access Controls',
          details: 'We implement strict access controls and authentication mechanisms to ensure only authorized personnel can access your data.'
        },
        {
          subtitle: 'Regular Audits',
          details: 'We conduct regular security audits and vulnerability assessments to maintain the highest security standards.'
        },
        {
          subtitle: 'Incident Response',
          details: 'We have established procedures for detecting, responding to, and reporting security incidents promptly.'
        }
      ]
    },
    {
      id: 'your-rights',
      title: 'Your Rights and Choices',
      icon: Shield,
      content: [
        {
          subtitle: 'Access and Portability',
          details: 'You have the right to access your personal data and request a copy in a portable format.'
        },
        {
          subtitle: 'Correction and Updates',
          details: 'You can update or correct your personal information through your account settings or by contacting us.'
        },
        {
          subtitle: 'Deletion',
          details: 'You can request deletion of your personal data, subject to legal and contractual obligations.'
        },
        {
          subtitle: 'Marketing Opt-out',
          details: 'You can opt out of marketing communications at any time through your account settings or unsubscribe links.'
        }
      ]
    },
    {
      id: 'data-retention',
      title: 'Data Retention',
      icon: Calendar,
      content: [
        {
          subtitle: 'Account Data',
          details: 'We retain your account information for as long as your account is active or as needed to provide services.'
        },
        {
          subtitle: 'Analysis Data',
          details: 'App review analysis data is retained for up to 2 years to enable historical comparisons and trend analysis.'
        },
        {
          subtitle: 'Legal Requirements',
          details: 'Some data may be retained longer to comply with legal obligations, resolve disputes, or enforce agreements.'
        },
        {
          subtitle: 'Deletion Process',
          details: 'When data is no longer needed, it is securely deleted or anonymized according to our data retention schedule.'
        }
      ]
    }
  ];

  const lastUpdated = 'January 15, 2025';

  return (
    <>
      <SEOHead {...seoConfig.privacyPolicy} />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/logo.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.history.back()}
                className="px-4 py-2 text-sm font-medium text-white bg-white/10 rounded-lg hover:bg-white/20 transition-colors backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Shield className="h-16 w-16 text-[#2DD4BF] mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-white mb-4">
                Privacy <span className="text-[#2DD4BF]">Policy</span>
              </h1>
              <p className="text-xl text-white/70 mb-6">
                Your privacy is important to us. This policy explains how we collect, use, and protect your information.
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-white/50">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Quick Summary */}
        <section className="py-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 mb-8">
              <h2 className="text-lg font-semibold text-white mb-4">Privacy Policy Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-white/80">
                <div className="flex items-start space-x-2">
                  <Eye className="h-4 w-4 mt-0.5 flex-shrink-0 text-[#2DD4BF]" />
                  <span>We collect only necessary information to provide our services</span>
                </div>
                <div className="flex items-start space-x-2">
                  <Lock className="h-4 w-4 mt-0.5 flex-shrink-0 text-[#2DD4BF]" />
                  <span>All data is encrypted and securely stored</span>
                </div>
                <div className="flex items-start space-x-2">
                  <Users className="h-4 w-4 mt-0.5 flex-shrink-0 text-[#2DD4BF]" />
                  <span>We never sell your personal information</span>
                </div>
                <div className="flex items-start space-x-2">
                  <Shield className="h-4 w-4 mt-0.5 flex-shrink-0 text-[#2DD4BF]" />
                  <span>You have full control over your data</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Detailed Sections */}
        <section className="py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {sections.map((section, index) => {
                const Icon = section.icon;
                const isExpanded = expandedSection === section.id;
                
                return (
                  <motion.div
                    key={section.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl"
                  >
                    <button
                      onClick={() => setExpandedSection(isExpanded ? null : section.id)}
                      className="w-full flex items-center justify-between p-6 text-left hover:bg-white/10 transition-colors rounded-2xl"
                    >
                      <div className="flex items-center space-x-4">
                        <Icon className="h-6 w-6 text-[#2DD4BF]" />
                        <h3 className="text-lg font-semibold text-white">{section.title}</h3>
                      </div>
                      {isExpanded ?
                        <ChevronDown className="h-5 w-5 text-white/50" /> :
                        <ChevronRight className="h-5 w-5 text-white/50" />
                      }
                    </button>

                    {isExpanded && (
                      <div className="border-t border-white/10 p-6 bg-white/5">
                        <div className="space-y-6">
                          {section.content.map((item, itemIndex) => (
                            <div key={itemIndex}>
                              <h4 className="font-semibold text-white mb-2">{item.subtitle}</h4>
                              <p className="text-white/70 leading-relaxed">{item.details}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Questions About Privacy?</h2>
            <p className="text-xl text-white/70 mb-8">
              If you have any questions about this Privacy Policy, please contact us.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <Mail className="h-8 w-8 text-[#2DD4BF] mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Email Us</h3>
                <p className="text-white/70 mb-4"><EMAIL></p>
                <button className="w-full bg-[#2DD4BF] text-[#0A1128] py-2 px-4 rounded-lg hover:bg-[#14B8A6] transition-colors font-medium">
                  Send Email
                </button>
              </div>
              
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
                <FileText className="h-8 w-8 text-[#2DD4BF] mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Data Request</h3>
                <p className="text-white/70 mb-4">Request your data or deletion</p>
                <button className="w-full bg-white/10 text-white py-2 px-4 rounded-lg hover:bg-white/20 transition-colors font-medium border border-white/20">
                  Submit Request
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
