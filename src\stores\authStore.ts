import { create } from 'zustand'
import { User } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'
import { createUserProfile, getUserUsage } from '../lib/database'
import { useReportStore } from './reportStore'

interface UserUsage {
  reportsGenerated: number
  freeReportsLimit: number
  remainingReports: number
}

interface AuthState {
  user: User | null
  userUsage: UserUsage | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string) => Promise<void>
  signInWithGoogle: () => Promise<void>
  signOut: () => Promise<void>
  initialize: () => Promise<void>
  refreshUserUsage: () => Promise<void>
  resendConfirmation: (email: string) => Promise<void>
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  userUsage: null,
  loading: true,

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) {
      // Handle specific error cases
      if (error.message.includes('Invalid login credentials')) {
        throw new Error('Invalid email or password. Please check your credentials.')
      } else if (error.message.includes('Email not confirmed')) {
        throw new Error('Please check your email and click the confirmation link before signing in.')
      } else if (error.message.includes('Email link is invalid or has expired')) {
        throw new Error('The confirmation link has expired. Please request a new one.')
      }
      throw error
    }
    set({ user: data.user })
    
    // Load user usage after successful sign in
    if (data.user) {
      try {
        const usage = await getUserUsage(data.user.id)
        set({ userUsage: usage })
      } catch (error) {
        console.error('Error loading user usage:', error)
      }
    }
  },

  signUp: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    if (error) throw error

    // Check if email confirmation is required
    if (data.user && !data.session) {
      // User was created but needs to confirm email
      // Don't set user in store yet, they need to confirm email first
      throw new Error('Please check your email and click the confirmation link before signing in.')
    }

    // User profile will be created automatically by the database trigger
    set({ user: data.user })
    
    // Set default usage for new users
    if (data.user) {
      set({ 
        userUsage: { 
          reportsGenerated: 0, 
          freeReportsLimit: 3, 
          remainingReports: 3 
        } 
      })
    }
  },

  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/`
      }
    })
    if (error) throw error
    
    // Note: User usage will be loaded in the onAuthStateChange callback
    // which is triggered after OAuth redirect completion
  },

  signOut: async () => {
    try {
      // Check if user is already signed out
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        // User is already signed out, just update state
        set({ user: null, userUsage: null })
        // Reset report store
        try {
          useReportStore.getState().resetStore()
        } catch (error) {
          console.warn('Error resetting report store:', error)
        }
        return
      }

      const { error } = await supabase.auth.signOut()
      if (error) {
        // If it's a session missing error, just update state
        if (error.message?.includes('session') || error.message?.includes('Auth session missing')) {
          console.warn('Session already expired, updating state')
          set({ user: null, userUsage: null })
          // Reset report store
          try {
            useReportStore.getState().resetStore()
          } catch (error) {
            console.warn('Error resetting report store:', error)
          }
          return
        }
        throw error
      }
      set({ user: null, userUsage: null })
      // Reset report store
      try {
        useReportStore.getState().resetStore()
      } catch (error) {
        console.warn('Error resetting report store:', error)
      }
    } catch (error) {
      console.error('Sign out error:', error)
      // Even if signOut fails, clear the local state
      set({ user: null, userUsage: null })
      // Reset report store
      try {
        useReportStore.getState().resetStore()
      } catch (resetError) {
        console.warn('Error resetting report store:', resetError)
      }
      // Don't throw the error to prevent UI crashes
    }
  },

  initialize: async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const user = session?.user ?? null
      set({ user, loading: false })

      // Load user usage if user is logged in
      if (user) {
        try {
          const usage = await getUserUsage(user.id)
          set({ userUsage: usage })
        } catch (error) {
          console.error('Error loading user usage during initialization:', error)
        }
      }

      supabase.auth.onAuthStateChange(async (event, session) => {
        const newUser = session?.user ?? null
        const currentUser = get().user
        
        // Only update if user actually changed
        if (newUser?.id !== currentUser?.id) {
          set({ user: newUser, loading: false })
          
          if (newUser) {
            try {
              const usage = await getUserUsage(newUser.id)
              set({ userUsage: usage })
            } catch (error) {
              console.error('Error loading user usage on auth change:', error)
            }
          } else {
            set({ userUsage: null })
            // Reset report store when user logs out
            try {
              useReportStore.getState().resetStore()
            } catch (error) {
              console.warn('Error resetting report store:', error)
            }
          }
        }
      })
    } catch (error) {
      console.error('Auth initialization error:', error)
      set({ loading: false })
    }
  },

  refreshUserUsage: async () => {
    const { user } = get()
    if (!user) return
    
    try {
      const usage = await getUserUsage(user.id)
      set({ userUsage: usage })
    } catch (error) {
      console.error('Error refreshing user usage:', error)
    }
  },

  resendConfirmation: async (email: string) => {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email,
    })
    if (error) throw error
  },
}))