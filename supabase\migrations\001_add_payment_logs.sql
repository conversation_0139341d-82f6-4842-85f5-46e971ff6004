-- 创建支付日志表
CREATE TABLE IF NOT EXISTS public.payment_logs (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES public.users(id) ON DELETE CASCADE,
    stripe_payment_intent_id text,
    stripe_checkout_session_id text,
    event_type text NOT NULL, -- payment_intent.created, payment_intent.succeeded, etc.
    payment_status text NOT NULL, -- created, processing, succeeded, failed, canceled
    amount integer, -- 金额（分）
    currency text DEFAULT 'usd',
    plan_name text, -- Starter, Professional, Business
    reports_purchased integer, -- 购买的报告次数
    error_message text, -- 失败时的错误信息
    metadata jsonb, -- 其他相关数据
    processed_at timestamptz DEFAULT now(),
    created_at timestamptz DEFAULT now()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_payment_logs_user_id ON public.payment_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_stripe_payment_intent_id ON public.payment_logs(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_stripe_checkout_session_id ON public.payment_logs(stripe_checkout_session_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_event_type ON public.payment_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_logs_payment_status ON public.payment_logs(payment_status);
CREATE INDEX IF NOT EXISTS idx_payment_logs_created_at ON public.payment_logs(created_at DESC);

-- 启用 RLS
ALTER TABLE public.payment_logs ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 政策
CREATE POLICY "Users can view their own payment logs" ON public.payment_logs
    FOR SELECT USING (auth.uid() = user_id);

-- 允许服务角色完全访问（用于 webhook）
CREATE POLICY "Service role can manage payment logs" ON public.payment_logs
    FOR ALL USING (auth.role() = 'service_role');

-- 添加注释
COMMENT ON TABLE public.payment_logs IS '支付日志表，记录所有支付相关事件';
COMMENT ON COLUMN public.payment_logs.stripe_payment_intent_id IS 'Stripe PaymentIntent ID';
COMMENT ON COLUMN public.payment_logs.stripe_checkout_session_id IS 'Stripe Checkout Session ID';
COMMENT ON COLUMN public.payment_logs.event_type IS 'Stripe webhook 事件类型';
COMMENT ON COLUMN public.payment_logs.payment_status IS '支付状态';
COMMENT ON COLUMN public.payment_logs.amount IS '支付金额（分）';
COMMENT ON COLUMN public.payment_logs.reports_purchased IS '购买的报告次数';
COMMENT ON COLUMN public.payment_logs.error_message IS '支付失败时的错误信息'; 