import React from 'react';
import { motion } from 'framer-motion';
import { Gamepad2, Trophy, Users, TrendingUp, Star, CheckCircle, ArrowRight, Target } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const GamingAppsPage: React.FC = () => {
  const features = [
    {
      icon: <Gamepad2 className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Player Sentiment Analysis",
      description: "Understand player emotions and satisfaction levels across different game features, levels, and updates."
    },
    {
      icon: <Trophy className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Gameplay Feedback Insights",
      description: "Analyze feedback on game mechanics, difficulty balance, progression systems, and monetization strategies."
    },
    {
      icon: <Users className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Community Sentiment Tracking",
      description: "Monitor player community reactions to events, updates, new content releases, and competitive features."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Retention & Engagement Analysis",
      description: "Identify factors that drive player retention and engagement through comprehensive review analysis."
    }
  ];

  const gameTypes = [
    {
      title: "Mobile RPGs",
      description: "Character progression, story engagement, and monetization balance analysis",
      insights: ["Character development feedback", "Story pacing analysis", "Gacha system sentiment", "Guild feature reviews"],
      metrics: "85% better retention insights"
    },
    {
      title: "Casual Games",
      description: "Accessibility, difficulty curves, and social features optimization",
      insights: ["Level design feedback", "Tutorial effectiveness", "Social sharing analysis", "Ad placement sentiment"],
      metrics: "92% improved user satisfaction"
    },
    {
      title: "Strategy Games",
      description: "Balance, complexity, and competitive gameplay analysis",
      insights: ["Balance patch reactions", "Competitive meta analysis", "Tutorial complexity", "Resource management feedback"],
      metrics: "78% better game balance"
    },
    {
      title: "Action Games",
      description: "Controls, performance, and gameplay mechanics optimization",
      insights: ["Control responsiveness", "Performance issues", "Combat system feedback", "Visual effects analysis"],
      metrics: "65% fewer performance complaints"
    }
  ];

  const challenges = [
    {
      challenge: "Player Retention",
      solution: "Identify specific gameplay elements that cause player churn",
      impact: "40% improvement in 30-day retention"
    },
    {
      challenge: "Monetization Balance",
      solution: "Optimize in-app purchases based on player sentiment analysis",
      impact: "25% increase in revenue per user"
    },
    {
      challenge: "Update Reception",
      solution: "Monitor player reactions to new content and features",
      impact: "60% faster issue identification"
    },
    {
      challenge: "Competitive Analysis",
      solution: "Compare player sentiment against competing games",
      impact: "3x better market positioning"
    }
  ];

  const metrics = [
    { value: "500M+", label: "Gaming Reviews Analyzed", description: "Comprehensive gaming industry dataset" },
    { value: "95%", label: "Sentiment Accuracy", description: "Gaming-specific sentiment models" },
    { value: "72%", label: "Faster Issue Detection", description: "Real-time gaming feedback monitoring" },
    { value: "45%", label: "Better Player Retention", description: "Data-driven game improvements" }
  ];

  return (
    <>
      <SEOHead 
        title="Gaming Apps Analytics - AppReview.Today | Player Sentiment & Game Optimization"
        description="Optimize your mobile game with player sentiment analysis. Understand player feedback, improve retention, balance gameplay, and boost app store ratings for gaming apps."
        canonical="/gaming-apps"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "Gaming Apps Analytics - AppReview.Today",
          "description": "Player sentiment analysis and game optimization platform for mobile gaming apps",
          "audience": {
            "@type": "Audience",
            "audienceType": "Game Developers"
          },
          "about": {
            "@type": "Thing",
            "name": "Mobile Gaming Analytics"
          }
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-white bg-white/10 hover:bg-white/20 font-medium transition-colors rounded-lg backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex justify-center mb-6">
                <Gamepad2 className="w-16 h-16 text-[#2DD4BF]" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
                Level Up Your <span className="text-[#2DD4BF]">Gaming App</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-white/70">
                Transform player feedback into winning game features. Optimize gameplay,
                boost retention, and create experiences players love.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-[#0A1128] px-8 py-3 rounded-lg font-semibold transition-colors">
                  Analyze Player Feedback
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View Gaming Case Studies
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Gaming-Specific Features */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Gaming-Focused Analytics
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Specialized tools designed specifically for mobile game developers and publishers
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg hover:bg-white/10 transition-all"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-white/70">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Game Types Analysis */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Specialized for Every Game Genre
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Tailored analytics for different types of mobile games
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {gameTypes.map((gameType, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl"
                >
                  <h3 className="text-xl font-bold text-white mb-3">
                    {gameType.title}
                  </h3>
                  <p className="text-white/70 mb-6">
                    {gameType.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {gameType.insights.map((insight, insightIndex) => (
                      <div key={insightIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-[#2DD4BF] flex-shrink-0" />
                        <span className="text-white/80">{insight}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-[#2DD4BF]/20 p-4 rounded-lg border border-[#2DD4BF]/30">
                    <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold">
                      <TrendingUp className="w-4 h-4" />
                      {gameType.metrics}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Gaming Challenges & Solutions */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Solve Common Gaming Challenges
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Address the biggest challenges facing mobile game developers today
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {challenges.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg"
                >
                  <div className="flex items-start gap-4 mb-4">
                    <Target className="w-6 h-6 text-red-400 flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">
                        Challenge: {item.challenge}
                      </h3>
                      <p className="text-white/70 mb-4">
                        {item.solution}
                      </p>
                      <div className="bg-[#2DD4BF]/20 p-3 rounded-lg border border-[#2DD4BF]/30">
                        <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold text-sm">
                          <TrendingUp className="w-4 h-4" />
                          {item.impact}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Gaming Metrics */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Proven Results for Gaming Apps
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                See how game developers are achieving better player satisfaction and retention
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl text-center"
                >
                  <div className="text-4xl font-bold text-[#2DD4BF] mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">
                    {metric.label}
                  </div>
                  <div className="text-white/70 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Level Up Your Game?
              </h2>
              <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
                Join successful game developers who are creating better player experiences with data-driven insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] text-[#0A1128] hover:bg-[#14B8A6] px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Start Gaming Analysis
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View Gaming Demo
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
      
      <Footer />
    </>
  );
};
