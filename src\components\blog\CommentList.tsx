import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { MessageCircle, Reply, User, Globe, Calendar } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { CommentForm } from './CommentForm';
import { supabase } from '../../lib/supabase';

interface Comment {
  id: string;
  author_name: string;
  author_email: string;
  author_website?: string;
  content: string;
  created_at: string;
  parent_id?: string;
  replies?: Comment[];
}

interface CommentListProps {
  postId: string;
}

export const CommentList: React.FC<CommentListProps> = ({ postId }) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  const fetchComments = async () => {
    try {
      const { data, error: fetchError } = await supabase
        .from('blog_comments')
        .select('*')
        .eq('post_id', postId)
        .eq('is_approved', true)
        .order('created_at', { ascending: true });

      if (fetchError) throw fetchError;

      // Organize comments into a tree structure
      const commentMap = new Map<string, Comment>();
      const rootComments: Comment[] = [];

      // First pass: create all comment objects
      data.forEach(comment => {
        commentMap.set(comment.id, {
          ...comment,
          replies: []
        });
      });

      // Second pass: organize into tree structure
      data.forEach(comment => {
        const commentObj = commentMap.get(comment.id)!;
        if (comment.parent_id) {
          const parent = commentMap.get(comment.parent_id);
          if (parent) {
            parent.replies!.push(commentObj);
          }
        } else {
          rootComments.push(commentObj);
        }
      });

      setComments(rootComments);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load comments');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [postId]);

  const handleCommentSubmitted = () => {
    fetchComments();
    setReplyingTo(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const CommentItem: React.FC<{ comment: Comment; depth?: number }> = ({ 
    comment, 
    depth = 0 
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${depth > 0 ? 'ml-8 mt-4' : 'mb-6'}`}
    >
      <Card className="p-4">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-[#2DD4BF]/20 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="w-5 h-5 text-[#2DD4BF]" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <h4 className="font-medium text-white">
                {comment.author_website ? (
                  <a
                    href={comment.author_website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-[#2DD4BF] transition-colors"
                  >
                    {comment.author_name}
                    <Globe className="w-3 h-3 inline ml-1" />
                  </a>
                ) : (
                  comment.author_name
                )}
              </h4>
              
              <span className="text-white/50 text-sm flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                {formatDate(comment.created_at)}
              </span>
            </div>
            
            <div className="text-white/80 mb-3 whitespace-pre-wrap">
              {comment.content}
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                icon={Reply}
                onClick={() => setReplyingTo(
                  replyingTo === comment.id ? null : comment.id
                )}
                className="text-white/60 hover:text-[#2DD4BF]"
              >
                Reply
              </Button>
            </div>
          </div>
        </div>
        
        {replyingTo === comment.id && (
          <div className="mt-4 ml-13">
            <CommentForm
              postId={postId}
              parentId={comment.id}
              onCommentSubmitted={handleCommentSubmitted}
              onCancel={() => setReplyingTo(null)}
              isReply={true}
            />
          </div>
        )}
      </Card>
      
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4">
          {comment.replies.map(reply => (
            <CommentItem
              key={reply.id}
              comment={reply}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </motion.div>
  );

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-4">
            <div className="animate-pulse">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-white/10 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-white/10 rounded w-1/4 mb-2"></div>
                  <div className="h-3 bg-white/10 rounded w-full mb-1"></div>
                  <div className="h-3 bg-white/10 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6 text-center">
        <MessageCircle className="w-12 h-12 text-white/30 mx-auto mb-3" />
        <p className="text-white/60">Failed to load comments</p>
        <Button
          variant="ghost"
          onClick={fetchComments}
          className="mt-2"
        >
          Try Again
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <MessageCircle className="w-5 h-5 text-[#2DD4BF]" />
        <h3 className="text-xl font-semibold text-white">
          Comments ({comments.length})
        </h3>
      </div>

      {comments.length === 0 ? (
        <Card className="p-8 text-center">
          <MessageCircle className="w-16 h-16 text-white/20 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-white mb-2">No comments yet</h4>
          <p className="text-white/60">Be the first to share your thoughts!</p>
        </Card>
      ) : (
        <div>
          {comments.map(comment => (
            <CommentItem key={comment.id} comment={comment} />
          ))}
        </div>
      )}

      <div className="mt-8">
        <CommentForm
          postId={postId}
          onCommentSubmitted={handleCommentSubmitted}
        />
      </div>
    </div>
  );
};
