import React from 'react';
import { motion } from 'framer-motion';
import { Bar<PERSON>hart3, Search, Brain, FileText, Download, ArrowRight, Smartphone, MessageSquare, TrendingUp } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { getPageSEO } from '../lib/seo-config';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { useNavigate } from 'react-router-dom';

export const HowItWorksPage: React.FC = () => {
  const navigate = useNavigate();
  const seoConfig = getPageSEO('howItWorks');

  const steps = [
    {
      icon: Search,
      title: 'Enter App Information',
      description: 'Simply provide your app name or competitor app details. Our system will automatically identify and locate your app across multiple platforms.',
      details: ['App Store (iOS)', 'Google Play Store (Android)', 'Reddit discussions', 'Automatic app detection']
    },
    {
      icon: Brain,
      title: 'AI-Powered Analysis',
      description: 'Our advanced AI scrapes and analyzes thousands of reviews using Google Gemini API for deep sentiment analysis and pattern recognition.',
      details: ['Multi-platform scraping', 'Sentiment analysis', 'Theme extraction', 'Pattern recognition']
    },
    {
      icon: FileText,
      title: 'Generate Insights',
      description: 'The AI processes all data to identify key themes, user pain points, feature requests, and actionable recommendations for improvement.',
      details: ['Key themes identification', 'Pain point analysis', 'Feature requests', 'Improvement recommendations']
    },
    {
      icon: Download,
      title: 'Export & Share',
      description: 'Receive a comprehensive PDF report with all insights, charts, and recommendations that you can easily share with your team.',
      details: ['Professional PDF format', 'Charts and visualizations', 'Executive summary', 'Team sharing ready']
    }
  ];

  const platforms = [
    {
      icon: Smartphone,
      name: 'App Store',
      description: 'iOS app reviews and ratings',
      color: 'text-blue-400'
    },
    {
      icon: Smartphone,
      name: 'Google Play',
      description: 'Android app reviews and feedback',
      color: 'text-green-400'
    },
    {
      icon: MessageSquare,
      name: 'Reddit',
      description: 'Community discussions and opinions',
      color: 'text-orange-400'
    }
  ];

  const features = [
    {
      icon: TrendingUp,
      title: 'Sentiment Analysis',
      description: 'Advanced AI determines positive, negative, and neutral sentiment patterns in user reviews.'
    },
    {
      icon: Brain,
      title: 'Theme Extraction',
      description: 'Automatically identifies and categorizes common themes and topics mentioned by users.'
    },
    {
      icon: FileText,
      title: 'Actionable Insights',
      description: 'Provides specific, actionable recommendations based on user feedback analysis.'
    }
  ];

  return (
    <>
      <SEOHead 
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        url="https://appreview.today/how-it-works"
        type={seoConfig.type}
        structuredData={seoConfig.structuredData}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white cursor-pointer" onClick={() => navigate('/')}>
                AppReview.Today
              </span>
            </motion.div>
            <Button onClick={() => navigate('/')}>
              Back to Home
            </Button>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-6xl font-bold text-white mb-6"
            >
              How <span className="text-blue-400">It Works</span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-300 mb-8 leading-relaxed"
            >
              Transform user reviews into actionable insights in just 4 simple steps
            </motion.p>
          </div>
        </section>

        {/* Process Steps */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6">
            <div className="space-y-16">
              {steps.map((step, index) => (
                <motion.div
                  key={step.title}
                  initial={{ opacity: 0, y: 40 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.2 }}
                  className="flex flex-col lg:flex-row items-center gap-12"
                >
                  {/* Step Number and Icon */}
                  <div className="flex-shrink-0 text-center lg:text-left">
                    <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-500 rounded-full mb-4">
                      <step.icon className="h-10 w-10 text-white" />
                    </div>
                    <div className="text-4xl font-bold text-blue-400 mb-2">
                      {String(index + 1).padStart(2, '0')}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <Card className="p-8">
                      <h3 className="text-2xl font-bold text-white mb-4">{step.title}</h3>
                      <p className="text-gray-300 text-lg mb-6 leading-relaxed">{step.description}</p>
                      <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center text-gray-400">
                            <ArrowRight className="h-4 w-4 text-blue-400 mr-2 flex-shrink-0" />
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </Card>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Supported Platforms */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-white mb-4">Supported Platforms</h2>
              <p className="text-gray-300 text-lg">We analyze reviews from multiple sources for comprehensive insights</p>
            </motion.div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {platforms.map((platform, index) => (
                <motion.div
                  key={platform.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="p-8 text-center h-full">
                    <platform.icon className={`h-12 w-12 ${platform.color} mx-auto mb-4`} />
                    <h3 className="text-xl font-semibold text-white mb-2">{platform.name}</h3>
                    <p className="text-gray-300">{platform.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Key Features */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-white mb-4">Powered by Advanced AI</h2>
              <p className="text-gray-300 text-lg">Our analysis engine provides deep insights you can act on</p>
            </motion.div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="p-8 text-center h-full">
                    <feature.icon className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                    <p className="text-gray-300">{feature.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
              <p className="text-gray-300 text-lg mb-8">
                Try our platform today and see how AI-powered review analysis can transform your app development process.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" onClick={() => navigate('/')}>
                  Start Free Analysis
                </Button>
                <Button variant="secondary" size="lg" onClick={() => navigate('/pricing')}>
                  View Pricing
                </Button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};
