import React from 'react'
import { motion } from 'framer-motion'
import { 
  Brain, 
  Zap, 
  Globe, 
  BarChart3, 
  Shield, 
  Download,
  Clock,
  Users,
  Target,
  Sparkles,
  TrendingUp,
  Filter
} from 'lucide-react'
import { SEOHead } from '../components/SEOHead'
import { seoConfig } from '../lib/seo-config'
import { Footer } from '../components/Footer'

const features = [
  {
    icon: Brain,
    title: "AI-Powered Analysis",
    description: "Advanced sentiment analysis using Google Gemini AI to understand user emotions and extract meaningful insights from reviews.",
    benefits: [
      "99.2% accuracy in sentiment detection",
      "Multi-language support",
      "Context-aware analysis",
      "Emotion mapping"
    ],
    category: "AI & Intelligence"
  },
  {
    icon: Zap,
    title: "Lightning Fast Processing",
    description: "Generate comprehensive reports in minutes, not hours. Our optimized pipeline processes thousands of reviews simultaneously.",
    benefits: [
      "Sub-5 minute report generation",
      "Parallel processing architecture",
      "Real-time progress tracking",
      "Batch processing for large datasets"
    ],
    category: "Performance"
  },
  {
    icon: Globe,
    title: "Multi-Platform Support",
    description: "Analyze reviews from App Store, Google Play, Reddit, and other major platforms in a single unified dashboard.",
    benefits: [
      "App Store & Google Play integration",
      "Reddit community analysis",
      "Cross-platform comparison",
      "Unified data visualization"
    ],
    category: "Integration"
  },
  {
    icon: BarChart3,
    title: "Advanced Analytics",
    description: "Deep dive into user feedback with comprehensive analytics, trend analysis, and actionable insights.",
    benefits: [
      "Sentiment trend analysis",
      "Theme extraction and clustering",
      "Rating correlation insights",
      "Competitive benchmarking"
    ],
    category: "Analytics"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-grade security with SOC 2 compliance, end-to-end encryption, and privacy-first architecture.",
    benefits: [
      "SOC 2 Type II certified",
      "End-to-end encryption",
      "GDPR compliant",
      "Zero data retention policy"
    ],
    category: "Security"
  },
  {
    icon: Download,
    title: "Flexible Export Options",
    description: "Export your analysis in multiple formats including PDF reports, CSV data, and API integrations.",
    benefits: [
      "Professional PDF reports",
      "Raw data CSV export",
      "API access for integrations",
      "Custom report templates"
    ],
    category: "Export & Integration"
  },
  {
    icon: Clock,
    title: "Real-Time Monitoring",
    description: "Stay updated with real-time review monitoring, alerts, and automated report generation.",
    benefits: [
      "Real-time review alerts",
      "Automated daily/weekly reports",
      "Critical issue notifications",
      "Trend change detection"
    ],
    category: "Monitoring"
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Share insights across your team with collaborative features, comments, and shared dashboards.",
    benefits: [
      "Team workspace management",
      "Shared report access",
      "Comment and annotation system",
      "Role-based permissions"
    ],
    category: "Collaboration"
  },
  {
    icon: Target,
    title: "Actionable Insights",
    description: "Get specific, actionable recommendations based on user feedback to improve your app's success.",
    benefits: [
      "Priority-ranked improvement suggestions",
      "Feature request identification",
      "Bug report categorization",
      "User journey optimization tips"
    ],
    category: "Insights"
  }
]

const categories = [
  "All Features",
  "AI & Intelligence", 
  "Performance",
  "Integration",
  "Analytics",
  "Security",
  "Export & Integration",
  "Monitoring",
  "Collaboration",
  "Insights"
]

export const FeaturesPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = React.useState("All Features")
  const [searchTerm, setSearchTerm] = React.useState("")

  const filteredFeatures = features.filter(feature => {
    const matchesCategory = selectedCategory === "All Features" || feature.category === selectedCategory
    const matchesSearch = feature.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         feature.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  return (
    <>
      <SEOHead {...seoConfig.features} />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-white bg-white/10 hover:bg-white/20 font-medium transition-colors rounded-lg backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center justify-center mb-6">
                <Sparkles className="h-8 w-8 text-[#2DD4BF] mr-3" />
                <h1 className="text-4xl md:text-5xl font-bold text-white">
                  Powerful <span className="text-[#2DD4BF]">Features</span>
                </h1>
              </div>
              <p className="text-xl text-white/70 max-w-3xl mx-auto mb-8">
                Discover the comprehensive suite of tools and capabilities that make AppReview.Today
                the leading platform for app review analysis and user insight generation.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-[#2DD4BF]">99.2%</div>
                  <div className="text-sm text-white/50">Accuracy Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-[#2DD4BF]">&lt;5min</div>
                  <div className="text-sm text-white/50">Report Generation</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-[#2DD4BF]">10+</div>
                  <div className="text-sm text-white/50">Platforms Supported</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-[#2DD4BF]">24/7</div>
                  <div className="text-sm text-white/50">Monitoring</div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Filter Section */}
        <section className="py-8 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                <input
                  type="text"
                  placeholder="Search features..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-[#2DD4BF] focus:border-transparent text-white placeholder-white/50"
                />
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-[#2DD4BF] text-[#0A1128]'
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl shadow-lg hover:shadow-xl transition-shadow p-6"
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-[#2DD4BF]/20 rounded-lg mr-4">
                      <feature.icon className="h-6 w-6 text-[#2DD4BF]" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">{feature.title}</h3>
                      <span className="text-sm text-[#2DD4BF] font-medium">{feature.category}</span>
                    </div>
                  </div>

                  <p className="text-white/70 mb-4">{feature.description}</p>

                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-white/70">
                        <TrendingUp className="h-4 w-4 text-[#2DD4BF] mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>

            {filteredFeatures.length === 0 && (
              <div className="text-center py-12">
                <p className="text-white/70 text-lg">No features found matching your criteria.</p>
                <button
                  onClick={() => {
                    setSelectedCategory("All Features")
                    setSearchTerm("")
                  }}
                  className="mt-4 px-6 py-2 bg-[#2DD4BF] text-[#0A1128] rounded-lg hover:bg-[#14B8A6] transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Experience These Features?
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Start your free trial today and see how AppReview.Today can transform your app development process.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => window.location.href = '/pricing'}
                  className="px-8 py-3 bg-[#2DD4BF] text-[#0A1128] rounded-lg font-semibold hover:bg-[#14B8A6] transition-colors"
                >
                  Start Free Trial
                </button>
                <button
                  onClick={() => window.location.href = '/pricing'}
                  className="px-8 py-3 bg-white/10 border-2 border-white/20 text-white rounded-lg font-semibold hover:bg-white/20 transition-colors backdrop-blur-sm"
                >
                  View Pricing
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>

      <Footer />
    </>
  )
}
