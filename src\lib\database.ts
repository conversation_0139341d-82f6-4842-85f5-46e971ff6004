import { supabase } from './supabase'
import { Database } from './supabase'

type Tables = Database['public']['Tables']

// User operations
export const createUserProfile = async (userId: string, email: string) => {
  const { data, error } = await supabase
    .from('users')
    .insert({ 
      id: userId, 
      email,
      reports_generated: 0,
      free_reports_limit: 3
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) throw error
  return data
}

export const getUserUsage = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('reports_generated, free_reports_limit')
    .eq('id', userId)
    .single()

  if (error) throw error
  return {
    reportsGenerated: data.reports_generated,
    freeReportsLimit: data.free_reports_limit,
    remainingReports: data.free_reports_limit - data.reports_generated
  }
}

export const increaseUserReportLimit = async (userId: string, additionalReports: number) => {
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('free_reports_limit')
    .eq('id', userId)
    .single()

  if (userError) throw userError

  const newLimit = userData.free_reports_limit + additionalReports

  const { data, error } = await supabase
    .from('users')
    .update({ 
      free_reports_limit: newLimit,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Payment logs operations
export const createPaymentLog = async (
  userId: string,
  eventType: string,
  paymentStatus: string,
  options: {
    stripePaymentIntentId?: string
    stripeCheckoutSessionId?: string
    amount?: number
    currency?: string
    planName?: string
    reportsPurchased?: number
    errorMessage?: string
    metadata?: any
  } = {}
) => {
  const { data, error } = await supabase
    .from('payment_logs')
    .insert({
      user_id: userId,
      event_type: eventType,
      payment_status: paymentStatus,
      stripe_payment_intent_id: options.stripePaymentIntentId,
      stripe_checkout_session_id: options.stripeCheckoutSessionId,
      amount: options.amount,
      currency: options.currency || 'usd',
      plan_name: options.planName,
      reports_purchased: options.reportsPurchased,
      error_message: options.errorMessage,
      metadata: options.metadata
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export const getUserPaymentLogs = async (userId: string, limit: number = 10) => {
  try {
    const { data, error } = await supabase
      .from('payment_logs')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.debug('Error fetching user payment logs (this is normal for new users):', error)
      return []
    }
    return data || []
  } catch (error) {
    console.debug('Payment logs fetch failed:', error)
    return []
  }
}

export const getLatestPaymentStatus = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('payment_logs')
      .select('payment_status, event_type, plan_name, reports_purchased, error_message, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle() // 使用 maybeSingle 而不是 single，避免在没有数据时抛出错误

    if (error) {
      console.debug('Error fetching latest payment status (this is normal for new users):', error)
      return null
    }
    return data
  } catch (error) {
    console.debug('Payment status fetch failed:', error)
    return null
  }
}

// Report operations
export const createReport = async (
  userId: string, 
  appName: string, 
  userSearchTerm?: string, 
  selectedAppName?: string, 
  enabledPlatforms?: string[],
  timeFilterDays?: number
) => {
  // Check user's current usage
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('reports_generated, free_reports_limit')
    .eq('id', userId)
    .single()

  if (userError) throw userError

  // Check if user has exceeded their free report limit
  if (userData.reports_generated >= userData.free_reports_limit) {
    throw new Error(`You have reached your limit of ${userData.free_reports_limit} free reports. Please upgrade to generate more reports.`)
  }

  // Create the report
  const { data, error } = await supabase
    .from('reports')
    .insert({
      user_id: userId,
      app_name: appName,
      user_search_term: userSearchTerm,
      selected_app_name: selectedAppName,
      enabled_platforms: enabledPlatforms || ['app_store', 'google_play', 'reddit'],
      time_filter_days: timeFilterDays || 90,
      status: 'pending'
    })
    .select()
    .single()

  if (error) throw error

  // Increment user's report count
  const { error: updateError } = await supabase
    .from('users')
    .update({ 
      reports_generated: userData.reports_generated + 1,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)

  if (updateError) {
    console.error('Failed to update user usage count:', updateError)
    // We don't throw here to avoid failing the report creation
    // but we should log this for monitoring
  }

  return data
}

export const getUserReports = async (userId: string) => {
  const { data, error } = await supabase
    .from('reports')
    .select('*, user_search_term, selected_app_name, enabled_platforms, time_filter_days')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}

export const getReport = async (reportId: string) => {
  const { data, error } = await supabase
    .from('reports')
    .select(`
      *,
      themes (
        *,
        quotes (*),
        suggestions (*)
      )
    `)
    .eq('id', reportId)
    .single()

  if (error) throw error
  return data
}

export const getReportWithScrapedData = async (reportId: string) => {
  const { data, error } = await supabase
    .from('reports')
    .select(`
      *,
      themes (
        *,
        quotes (*),
        suggestions (*)
      ),
      scraping_sessions (
        *,
        scraped_reviews (*)
      )
    `)
    .eq('id', reportId)
    .single()

  if (error) throw error
  return data
}

export const updateReportStatus = async (
  reportId: string,
  status: 'pending' | 'scraping' | 'scraping_completed' | 'analyzing' | 'completing' | 'completed' | 'failed' | 'error',
  completedAt?: string
) => {
  const updates: any = { status }
  if (completedAt) updates.completed_at = completedAt

  const { data, error } = await supabase
    .from('reports')
    .update(updates)
    .eq('id', reportId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Scraping session operations
export const createScrapingSession = async (reportId: string, appName: string) => {
  const { data, error } = await supabase
    .from('scraping_sessions')
    .insert({
      report_id: reportId,
      app_name: appName,
      status: 'pending'
    })
    .select()
    .single()

  if (error) throw error
  return data
}

export const updateScrapingSession = async (
  sessionId: string,
  updates: {
    status?: 'pending' | 'running' | 'completed' | 'error'
    total_reviews_found?: number
    app_store_reviews?: number
    google_play_reviews?: number
    reddit_posts?: number
    error_message?: string
    completed_at?: string
  }
) => {
  const { data, error } = await supabase
    .from('scraping_sessions')
    .update(updates)
    .eq('id', sessionId)
    .select()
    .single()

  if (error) throw error
  return data
}

export const saveScrapedReviews = async (
  sessionId: string,
  reviews: Array<{
    platform: 'app_store' | 'google_play' | 'reddit'
    review_text: string
    rating?: number
    review_date?: string
    author_name?: string
    source_url?: string
    additional_data?: any
  }>
) => {
  const reviewsToInsert = reviews.map(review => ({
    scraping_session_id: sessionId,
    ...review
  }))

  const { data, error } = await supabase
    .from('scraped_reviews')
    .insert(reviewsToInsert)
    .select()

  if (error) throw error
  return data
}

export const getScrapingSession = async (sessionId: string) => {
  const { data, error } = await supabase
    .from('scraping_sessions')
    .select(`
      *,
      scraped_reviews (*)
    `)
    .eq('id', sessionId)
    .single()

  if (error) throw error
  return data
}

// Theme operations
export const createTheme = async (
  reportId: string,
  title: string,
  description: string
) => {
  const { data, error } = await supabase
    .from('themes')
    .insert({
      report_id: reportId,
      title,
      description
    })
    .select()
    .single()

  if (error) throw error
  return data
}

// Quote operations
export const createQuote = async (
  themeId: string,
  text: string,
  source: string,
  reviewDate: string
) => {
  const { data, error } = await supabase
    .from('quotes')
    .insert({
      theme_id: themeId,
      text,
      source,
      review_date: reviewDate
    })
    .select()
    .single()

  if (error) throw error
  return data
}

// Suggestion operations
export const createSuggestion = async (themeId: string, text: string) => {
  const { data, error } = await supabase
    .from('suggestions')
    .insert({
      theme_id: themeId,
      text
    })
    .select()
    .single()

  if (error) throw error
  return data
}