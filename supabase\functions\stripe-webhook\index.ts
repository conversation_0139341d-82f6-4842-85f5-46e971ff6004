import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import Stripe from 'https://esm.sh/stripe@14.21.0?target=deno'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, stripe-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

// Helper function to create payment log
async function createPaymentLog(userId: string, event: Stripe.Event, amount: number, currency: string, planName: string, reportsPurchased: number) {
  try {
    console.log(`[${new Date().toISOString()}] ===== DATABASE INSERT: Creating payment log =====`)
    console.log(`[${new Date().toISOString()}] User ID: ${userId}`)
    console.log(`[${new Date().toISOString()}] Amount: ${amount}`)
    console.log(`[${new Date().toISOString()}] Currency: ${currency}`)
    console.log(`[${new Date().toISOString()}] Plan: ${planName}`)
    console.log(`[${new Date().toISOString()}] Reports: ${reportsPurchased}`)
    console.log(`[${new Date().toISOString()}] Event type: ${event.type}`)
    
    const insertData = {
      user_id: userId,
      event_type: event.type,
      payment_status: 'succeeded',
      amount: amount,
      currency: currency,
      plan_name: planName,
      reports_purchased: reportsPurchased,
      metadata: event.data.object
    }
    
    console.log(`[${new Date().toISOString()}] Insert data:`, JSON.stringify(insertData, null, 2))
    
    const { data, error } = await supabase
      .from('payment_logs')
      .insert(insertData)
      .select()
    
    if (error) {
      console.error(`[${new Date().toISOString()}] ===== DATABASE ERROR: Payment log insert failed =====`)
      console.error(`[${new Date().toISOString()}] Error details:`, error)
      console.error(`[${new Date().toISOString()}] Error code:`, error.code)
      console.error(`[${new Date().toISOString()}] Error message:`, error.message)
      console.error(`[${new Date().toISOString()}] Error details:`, error.details)
      throw error
    }
    
    console.log(`[${new Date().toISOString()}] ===== DATABASE SUCCESS: Payment log created =====`)
    console.log(`[${new Date().toISOString()}] Created record:`, data)
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ===== DATABASE EXCEPTION: Payment log creation failed =====`)
    console.error(`[${new Date().toISOString()}] Exception:`, error)
    throw error
  }
}

Deno.serve(async (req: Request) => {
  console.log(`[${new Date().toISOString()}] Webhook received: ${req.method} ${req.url}`)
  
  // Test database connection
  try {
    console.log(`[${new Date().toISOString()}] ===== DATABASE TEST: Testing connection =====`)
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    if (testError) {
      console.error(`[${new Date().toISOString()}] ===== DATABASE ERROR: Connection test failed =====`)
      console.error(`[${new Date().toISOString()}] Error:`, testError)
    } else {
      console.log(`[${new Date().toISOString()}] ===== DATABASE SUCCESS: Connection test passed =====`)
    }
  } catch (error) {
    console.error(`[${new Date().toISOString()}] ===== DATABASE EXCEPTION: Connection test failed =====`)
    console.error(`[${new Date().toISOString()}] Exception:`, error)
  }
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const body = await req.text()
    console.log(`[${new Date().toISOString()}] Webhook body length: ${body.length}`)
    
    const signature = req.headers.get('stripe-signature')
    console.log(`[${new Date().toISOString()}] Stripe signature present: ${!!signature}`)
    
    if (!signature) {
      console.error('No stripe-signature header found')
      return new Response('No signature', { status: 400, headers: corsHeaders })
    }

    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')
    console.log(`[${new Date().toISOString()}] Webhook secret present: ${!!webhookSecret}`)
    
    if (!webhookSecret) {
      console.error('STRIPE_WEBHOOK_SECRET not configured')
      return new Response('Webhook secret not configured', { status: 500, headers: corsHeaders })
    }

    let event: Stripe.Event
    
    try {
      // Temporarily skip signature verification to avoid crypto issues
      // TODO: Implement proper signature verification later
      event = JSON.parse(body) as Stripe.Event
      console.log(`[${new Date().toISOString()}] Event parsed successfully: ${event.type}`)
    } catch (err) {
      console.error('Error parsing webhook body:', err)
      return new Response('Invalid payload', { status: 400, headers: corsHeaders })
    }

    console.log(`[${new Date().toISOString()}] Processing event: ${event.type}`)

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session
        console.log(`[${new Date().toISOString()}] Processing checkout session: ${session.id}`)
        console.log(`[${new Date().toISOString()}] Session data:`, {
          id: session.id,
          payment_status: session.payment_status,
          customer_email: session.customer_email,
          amount_total: session.amount_total,
          currency: session.currency,
          metadata: session.metadata,
          line_items: session.line_items,
          mode: session.mode,
          payment_intent: session.payment_intent,
          subscription: session.subscription
        })
        
        // Check if payment is successful - only process if payment is actually paid
        if (session.payment_status === 'paid' || session.payment_status === 'complete') {
          console.log(`[${new Date().toISOString()}] Payment appears successful, customer_email: ${session.customer_email}`)
          
          // Get user email from session metadata or customer_email
          let userEmail = session.customer_email
          let userId = null
          
          if (session.metadata) {
            console.log(`[${new Date().toISOString()}] Session metadata:`, session.metadata)
            userId = session.metadata.userId
            // If no customer_email but we have userId, we'll look up the user by ID
            if (!userEmail && userId) {
              console.log(`[${new Date().toISOString()}] No customer_email but found userId in metadata: ${userId}`)
            }
          }
          
          if (!userEmail && !userId) {
            console.error(`[${new Date().toISOString()}] No customer email or userId found in session`)
            break
          }
          
          try {
            // Update user's free reports limit
            let user: any = null
            
            if (userId) {
              // Look up user by ID from metadata
              console.log(`[${new Date().toISOString()}] ===== DATABASE SELECT: Looking up user by ID =====`)
              console.log(`[${new Date().toISOString()}] User ID: ${userId}`)
              
              const { data: userData, error: userError } = await supabase
                .from('users')
                .select('id, free_reports_limit, email')
                .eq('id', userId)
                .single()

              if (userError) {
                console.error(`[${new Date().toISOString()}] ===== DATABASE ERROR: User lookup by ID failed =====`)
                console.error(`[${new Date().toISOString()}] Error details:`, userError)
                console.error(`[${new Date().toISOString()}] Error code:`, userError.code)
                console.error(`[${new Date().toISOString()}] Error message:`, userError.message)
                throw userError
              } else {
                console.log(`[${new Date().toISOString()}] ===== DATABASE SUCCESS: User found by ID =====`)
                console.log(`[${new Date().toISOString()}] User data:`, userData)
                user = userData
              }
            } else if (userEmail) {
              // Look up user by email
              console.log(`[${new Date().toISOString()}] Looking up user with email: ${userEmail}`)
              
              const { data: userData, error: userError } = await supabase
                .from('users')
                .select('id, free_reports_limit, email')
                .eq('email', userEmail)
                .single()

              if (userError) {
                console.error(`[${new Date().toISOString()}] Error fetching user:`, userError)
                
                // Try case-insensitive search
                console.log(`[${new Date().toISOString()}] Trying case-insensitive email search`)
                const { data: userCaseInsensitive, error: userCaseError } = await supabase
                  .from('users')
                  .select('id, free_reports_limit, email')
                  .ilike('email', userEmail)
                  .single()
                
                                if (userCaseError) {
                  console.error(`[${new Date().toISOString()}] Case-insensitive search also failed:`, userCaseError)
                  console.log(`[${new Date().toISOString()}] ===== DATABASE INSERT: Creating new user =====`)
                  console.log(`[${new Date().toISOString()}] Email: ${userEmail}`)
                  
                  const createData = {
                    email: userEmail,
                    reports_generated: 0,
                    free_reports_limit: 3
                  }
                  
                  console.log(`[${new Date().toISOString()}] Create data:`, JSON.stringify(createData, null, 2))
                  
                  // Create user profile if not exists
                  const { data: newUser, error: createError } = await supabase
                    .from('users')
                    .insert(createData)
                    .select()
                    .single()
                
                  if (createError) {
                    console.error(`[${new Date().toISOString()}] ===== DATABASE ERROR: User creation failed =====`)
                    console.error(`[${new Date().toISOString()}] Error details:`, createError)
                    console.error(`[${new Date().toISOString()}] Error code:`, createError.code)
                    console.error(`[${new Date().toISOString()}] Error message:`, createError.message)
                    throw createError
                  }
                
                  console.log(`[${new Date().toISOString()}] ===== DATABASE SUCCESS: New user created =====`)
                  console.log(`[${new Date().toISOString()}] Created user:`, newUser)
                  // Continue with the new user
                  user = newUser
                } else {
                  console.log(`[${new Date().toISOString()}] Found user with case-insensitive search:`, userCaseInsensitive)
                  user = userCaseInsensitive
                }
              } else {
                console.log(`[${new Date().toISOString()}] Found user:`, userData)
                user = userData
              }
            }

            if (user) {
              console.log(`[${new Date().toISOString()}] ===== PROCESSING: Starting payment processing =====`)
              console.log(`[${new Date().toISOString()}] User found:`, user)
              
              // Determine reports purchased based on multiple factors
              const amount = session.amount_total || 0
              let reportsPurchased = 0
              let planName = 'Unknown Plan'
              let priceId = null
              
              // First, check session metadata (highest priority)
              if (session.metadata) {
                console.log(`[${new Date().toISOString()}] Checking session metadata:`, session.metadata)
                if (session.metadata.reports) {
                  reportsPurchased = parseInt(session.metadata.reports)
                  planName = session.metadata.plan_name || 'Custom Plan'
                  console.log(`[${new Date().toISOString()}] Found reports in session metadata: ${reportsPurchased}`)
                }
              }
              
              // If no reports info from session metadata, try line_items
              if (reportsPurchased === 0 && session.line_items && session.line_items.data && session.line_items.data.length > 0) {
                const lineItem = session.line_items.data[0]
                console.log(`[${new Date().toISOString()}] Line item data:`, lineItem)
                
                priceId = lineItem.price?.id
                const quantity = lineItem.quantity || 1
                
                // Check if we have metadata with reports info
                if (lineItem.price?.metadata?.reports) {
                  reportsPurchased = parseInt(lineItem.price.metadata.reports) * quantity
                  planName = lineItem.price.metadata.plan_name || 'Custom Plan'
                  console.log(`[${new Date().toISOString()}] Found reports in price metadata: ${reportsPurchased}`)
                } else if (lineItem.price?.metadata?.plan_name) {
                  planName = lineItem.price.metadata.plan_name
                  // Fall back to amount-based calculation
                }
              }
              
              // Fall back to amount-based calculation if no metadata found
              if (reportsPurchased === 0) {
                console.log(`[${new Date().toISOString()}] No metadata found, using amount-based calculation`)
                if (amount === 499) { // $4.99 - Starter
                  reportsPurchased = 3
                  planName = 'Starter'
                } else if (amount === 999) { // $9.99 - Professional
                  reportsPurchased = 10
                  planName = 'Professional'
                } else if (amount === 1999) { // $19.99 - Business
                  reportsPurchased = 25
                  planName = 'Business'
                } else {
                  console.log(`[${new Date().toISOString()}] Unknown amount: ${amount}, defaulting to 0 reports`)
                  reportsPurchased = 0
                  planName = 'Unknown Amount'
                }
              }

              console.log(`[${new Date().toISOString()}] Final calculation: amount=${amount}, reports=${reportsPurchased}, plan=${planName}, price_id=${priceId}`)

              const newLimit = user.free_reports_limit + reportsPurchased
              
              console.log(`[${new Date().toISOString()}] ===== DATABASE UPDATE: Updating user =====`)
              console.log(`[${new Date().toISOString()}] User ID: ${user.id}`)
              console.log(`[${new Date().toISOString()}] Current limit: ${user.free_reports_limit}`)
              console.log(`[${new Date().toISOString()}] New limit: ${newLimit}`)
              console.log(`[${new Date().toISOString()}] Reports purchased: ${reportsPurchased}`)
              
              const updateData = { 
                free_reports_limit: newLimit,
                updated_at: new Date().toISOString()
              }
              
              console.log(`[${new Date().toISOString()}] Update data:`, JSON.stringify(updateData, null, 2))
              
              const { data: updatedUser, error: updateError } = await supabase
                .from('users')
                .update(updateData)
                .eq('id', user.id)
                .select()
                .single()

              if (updateError) {
                console.error(`[${new Date().toISOString()}] ===== DATABASE ERROR: User update failed =====`)
                console.error(`[${new Date().toISOString()}] Error details:`, updateError)
                console.error(`[${new Date().toISOString()}] Error code:`, updateError.code)
                console.error(`[${new Date().toISOString()}] Error message:`, updateError.message)
                console.error(`[${new Date().toISOString()}] Error details:`, updateError.details)
                throw updateError
              }

              console.log(`[${new Date().toISOString()}] ===== DATABASE SUCCESS: User updated =====`)
              console.log(`[${new Date().toISOString()}] Updated user:`, updatedUser)

              // Create payment log with additional info
              await createPaymentLog(
                user.id,
                event,
                amount,
                session.currency || 'usd',
                planName,
                reportsPurchased
              )

              console.log(`[${new Date().toISOString()}] Webhook processed successfully for user: ${session.customer_email}`)
            } else {
              console.error(`[${new Date().toISOString()}] User object is null after lookup/creation`)
            }
          } catch (error) {
            console.error(`[${new Date().toISOString()}] Error processing checkout session:`, error)
            throw error
          }
        } else {
          console.log(`[${new Date().toISOString()}] Payment status not successful: ${session.payment_status}`)
        }
        break
        
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        console.log(`[${new Date().toISOString()}] Payment intent succeeded: ${paymentIntent.id}`)
        console.log(`[${new Date().toISOString()}] Payment intent data:`, {
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          customer: paymentIntent.customer,
          metadata: paymentIntent.metadata,
          receipt_email: paymentIntent.receipt_email,
          description: paymentIntent.description
        })
        
        // If we have customer email or metadata, we can process this payment
        if (paymentIntent.receipt_email || paymentIntent.metadata) {
          console.log(`[${new Date().toISOString()}] Processing payment intent with email: ${paymentIntent.receipt_email}`)
          console.log(`[${new Date().toISOString()}] Payment intent metadata:`, paymentIntent.metadata)
          
          try {
            // Look up user by email or metadata userId
            let user: any = null
            let userEmail = paymentIntent.receipt_email
            let userId = paymentIntent.metadata?.userId
            
            if (userId) {
              // Look up user by ID from metadata
              console.log(`[${new Date().toISOString()}] Looking up user by ID from payment intent: ${userId}`)
              const { data: userData, error: userError } = await supabase
                .from('users')
                .select('id, free_reports_limit, email')
                .eq('id', userId)
                .single()

              if (userError) {
                console.error(`[${new Date().toISOString()}] Error fetching user by ID from payment intent:`, userError)
              } else {
                console.log(`[${new Date().toISOString()}] Found user by ID from payment intent:`, userData)
                user = userData
              }
            }
            
            if (!user && userEmail) {
              // Fall back to email lookup
              const { data: userData, error: userError } = await supabase
                .from('users')
                .select('id, free_reports_limit, email')
                .eq('email', userEmail)
                .single()

              if (userError) {
                // Try case-insensitive search
                const { data: userCaseInsensitive, error: userCaseError } = await supabase
                  .from('users')
                  .select('id, free_reports_limit, email')
                  .ilike('email', userEmail)
                  .single()
                
                if (userCaseError) {
                  console.log(`[${new Date().toISOString()}] User not found for payment intent, creating new user`)
                  const { data: newUser, error: createError } = await supabase
                    .from('users')
                    .insert({
                      email: userEmail,
                      reports_generated: 0,
                      free_reports_limit: 3
                    })
                    .select()
                    .single()
                  
                  if (createError) {
                    console.error(`[${new Date().toISOString()}] Error creating user for payment intent:`, createError)
                  } else {
                    user = newUser
                  }
                } else {
                  user = userCaseInsensitive
                }
              } else {
                user = userData
              }
            }

            if (user) {
              // Determine reports from metadata or amount
              const amount = paymentIntent.amount || 0
              let reportsPurchased = 0
              let planName = 'Unknown Plan'
              
              // Check metadata first
              if (paymentIntent.metadata?.reports) {
                reportsPurchased = parseInt(paymentIntent.metadata.reports)
                planName = paymentIntent.metadata.plan_name || 'Custom Plan'
                console.log(`[${new Date().toISOString()}] Found reports in payment intent metadata: ${reportsPurchased}`)
              } else {
                // Fall back to amount-based calculation
                if (amount === 499) { // $4.99 - Starter
                  reportsPurchased = 3
                  planName = 'Starter'
                } else if (amount === 999) { // $9.99 - Professional
                  reportsPurchased = 10
                  planName = 'Professional'
                } else if (amount === 1999) { // $19.99 - Business
                  reportsPurchased = 25
                  planName = 'Business'
                }
              }

              if (reportsPurchased > 0) {
                const newLimit = user.free_reports_limit + reportsPurchased
                
                console.log(`[${new Date().toISOString()}] Updating user from payment intent: ${user.free_reports_limit} -> ${newLimit}`)
                
                const { error: updateError } = await supabase
                  .from('users')
                  .update({ 
                    free_reports_limit: newLimit,
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', user.id)

                if (updateError) {
                  console.error(`[${new Date().toISOString()}] Error updating user from payment intent:`, updateError)
                } else {
                  console.log(`[${new Date().toISOString()}] User updated successfully from payment intent`)
                  
                  // Create payment log
                  await createPaymentLog(
                    user.id,
                    event,
                    amount,
                    paymentIntent.currency || 'usd',
                    planName,
                    reportsPurchased
                  )
                }
              }
            }
          } catch (error) {
            console.error(`[${new Date().toISOString()}] Error processing payment intent:`, error)
          }
        }
        break
        
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent
        console.log(`[${new Date().toISOString()}] Payment failed: ${failedPayment.id}`)
        break
        
      default:
        console.log(`[${new Date().toISOString()}] Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (err) {
    console.error(`[${new Date().toISOString()}] Webhook processing failed:`, err)
    return new Response(
      `Webhook processing failed: ${err.message}`,
      { status: 400, headers: corsHeaders }
    )
  }
}) 