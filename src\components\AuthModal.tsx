import React, { useState } from 'react'
import { Modal } from './ui/Modal'
import { Button } from './ui/Button'
import { Input } from './ui/Input'
import { useAuthStore } from '../stores/authStore'
import { LogIn, UserPlus } from 'lucide-react'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
}

export const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose }) => {
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [confirmationEmail, setConfirmationEmail] = useState('')

  const { signIn, signUp, signInWithGoogle, resendConfirmation } = useAuthStore()

  const handleClose = () => {
    setIsSignUp(false)
    setEmail('')
    setPassword('')
    setError('')
    setShowConfirmation(false)
    setConfirmationEmail('')
    setLoading(false)
    onClose()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      if (isSignUp) {
        await signUp(email, password)
        handleClose()
      } else {
        await signIn(email, password)
        handleClose()
      }
    } catch (err: any) {
      if (err.message?.includes('check your email and click the confirmation link')) {
        setShowConfirmation(true)
        setConfirmationEmail(email)
        setError('')
      } else {
        setError(err.message || 'An error occurred')
      }
    } finally {
      setLoading(false)
    }
  }

  const toggleMode = () => {
    setIsSignUp(!isSignUp)
    setError('')
    setShowConfirmation(false)
    setConfirmationEmail('')
  }

  const handleResendConfirmation = async () => {
    setLoading(true)
    setError('')

    try {
      await resendConfirmation(confirmationEmail)
      setError('Confirmation email sent! Please check your inbox.')
    } catch (err: any) {
      setError(err.message || 'Failed to resend confirmation email')
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setLoading(true)
    setError('')

    try {
      await signInWithGoogle()
      // Note: The redirect will happen automatically, so we don't close the modal here
    } catch (err: any) {
      setError(err.message || 'Google sign in failed')
      setLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={showConfirmation ? 'Check Your Email' : (isSignUp ? 'Create Account' : 'Sign In')}
    >
      {showConfirmation ? (
        <div className="space-y-4">
          <div className="text-center space-y-3">
            <div className="text-white/80">
              We've sent a confirmation link to:
            </div>
            <div className="text-[#2DD4BF] font-medium">
              {confirmationEmail}
            </div>
            <div className="text-white/60 text-sm">
              Please click the link in your email to confirm your account, then return here to sign in.
            </div>
          </div>

          {error && (
            <div className={`text-sm border rounded-lg p-3 ${
              error.includes('sent') 
                ? 'text-green-400 bg-green-400/10 border-green-400/20'
                : 'text-red-400 bg-red-400/10 border-red-400/20'
            }`}>
              {error}
            </div>
          )}

          <div className="space-y-3">
            <Button
              type="button"
              onClick={handleResendConfirmation}
              className="w-full"
              loading={loading}
            >
              Resend Confirmation Email
            </Button>
            
            <Button
              type="button"
              onClick={() => {
                setShowConfirmation(false)
                setConfirmationEmail('')
                setError('')
              }}
              className="w-full bg-transparent border border-white/20 hover:bg-white/5"
            >
              Back to Sign In
            </Button>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            type="email"
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
          <Input
            type="password"
            label="Password"
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
          
          {error && (
            <div className="text-red-400 text-sm bg-red-400/10 border border-red-400/20 rounded-lg p-3">
              {error}
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            icon={isSignUp ? UserPlus : LogIn}
            loading={loading}
          >
            {isSignUp ? 'Create Account' : 'Sign In'}
          </Button>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-white/20"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-[#0A1128] text-white/60">Or continue with</span>
            </div>
          </div>

          <Button
            type="button"
            onClick={handleGoogleSignIn}
            className="w-full bg-white hover:bg-gray-100 text-gray-900 border border-gray-300"
            loading={loading}
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
          </Button>

          <div className="text-center">
            <button
              type="button"
              onClick={toggleMode}
              className="text-[#2DD4BF] hover:text-[#14B8A6] text-sm transition-colors"
            >
              {isSignUp
                ? 'Already have an account? Sign in'
                : "Don't have an account? Sign up"
              }
            </button>
          </div>
        </form>
      )}
    </Modal>
  )
}