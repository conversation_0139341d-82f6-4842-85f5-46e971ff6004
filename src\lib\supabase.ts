import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(`Missing Supabase environment variables. VITE_SUPABASE_URL: ${supabaseUrl}, VITE_SUPABASE_ANON_KEY: ${supabaseAnonKey ? 'present' : 'missing'}. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file.`)
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          created_at: string
          updated_at: string
          reports_generated: number
          free_reports_limit: number
        }
        Insert: {
          id?: string
          email: string
          created_at?: string
          updated_at?: string
          reports_generated?: number
          free_reports_limit?: number
        }
        Update: {
          id?: string
          email?: string
          created_at?: string
          updated_at?: string
          reports_generated?: number
          free_reports_limit?: number
        }
      }
      reports: {
        Row: {
          id: string
          user_id: string
          app_name: string
          status: 'pending' | 'scraping' | 'scraping_completed' | 'analyzing' | 'completing' | 'completed' | 'failed' | 'error'
          created_at: string
          completed_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          app_name: string
          status?: 'pending' | 'scraping' | 'scraping_completed' | 'analyzing' | 'completing' | 'completed' | 'failed' | 'error'
          created_at?: string
          completed_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          app_name?: string
          status?: 'pending' | 'scraping' | 'scraping_completed' | 'analyzing' | 'completing' | 'completed' | 'failed' | 'error'
          created_at?: string
          completed_at?: string | null
        }
      }
      themes: {
        Row: {
          id: string
          report_id: string
          title: string
          description: string
          created_at: string
        }
        Insert: {
          id?: string
          report_id: string
          title: string
          description: string
          created_at?: string
        }
        Update: {
          id?: string
          report_id?: string
          title?: string
          description?: string
          created_at?: string
        }
      }
      quotes: {
        Row: {
          id: string
          theme_id: string
          text: string
          source: string
          review_date: string
          created_at: string
        }
        Insert: {
          id?: string
          theme_id: string
          text: string
          source: string
          review_date: string
          created_at?: string
        }
        Update: {
          id?: string
          theme_id?: string
          text?: string
          source?: string
          review_date?: string
          created_at?: string
        }
      }
      suggestions: {
        Row: {
          id: string
          theme_id: string
          text: string
          created_at: string
        }
        Insert: {
          id?: string
          theme_id: string
          text: string
          created_at?: string
        }
        Update: {
          id?: string
          theme_id?: string
          text?: string
          created_at?: string
        }
      }
      payment_logs: {
        Row: {
          id: string
          user_id: string
          stripe_payment_intent_id: string | null
          stripe_checkout_session_id: string | null
          event_type: string
          payment_status: string
          amount: number | null
          currency: string
          plan_name: string | null
          reports_purchased: number | null
          error_message: string | null
          metadata: any | null
          processed_at: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_payment_intent_id?: string | null
          stripe_checkout_session_id?: string | null
          event_type: string
          payment_status: string
          amount?: number | null
          currency?: string
          plan_name?: string | null
          reports_purchased?: number | null
          error_message?: string | null
          metadata?: any | null
          processed_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_payment_intent_id?: string | null
          stripe_checkout_session_id?: string | null
          event_type?: string
          payment_status?: string
          amount?: number | null
          currency?: string
          plan_name?: string | null
          reports_purchased?: number | null
          error_message?: string | null
          metadata?: any | null
          processed_at?: string
          created_at?: string
        }
      }
    }
  }
}