import React from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  TrendingUp, 
  Bug, 
  Star,
  Target,
  Lightbulb,
  Shield,
  Zap,
  BarChart3,
  CheckCircle,
  ArrowRight,
  Briefcase
} from 'lucide-react'
import { SEOHead } from '../components/SEOHead'
import { seoConfig } from '../lib/seo-config'
import { Footer } from '../components/Footer'

const useCases = [
  {
    icon: Users,
    title: "Product Management",
    subtitle: "Make data-driven product decisions",
    description: "Transform user feedback into actionable product roadmaps and feature prioritization strategies.",
    challenges: [
      "Difficulty prioritizing feature requests",
      "Unclear user pain points",
      "Limited insight into user satisfaction",
      "Manual review analysis taking too long"
    ],
    solutions: [
      "Automated feature request extraction",
      "Sentiment-based priority scoring",
      "User journey pain point identification",
      "Real-time satisfaction tracking"
    ],
    outcomes: [
      "40% faster feature development cycles",
      "25% increase in user satisfaction scores",
      "60% reduction in analysis time",
      "Data-driven roadmap decisions"
    ],
    persona: "Product Manager",
    industry: "All Industries"
  },
  {
    icon: Bug,
    title: "Quality Assurance",
    subtitle: "Identify and resolve issues faster",
    description: "Automatically detect, categorize, and prioritize bugs and technical issues from user reviews.",
    challenges: [
      "Missing critical bugs in production",
      "Unclear bug reproduction steps",
      "Difficulty prioritizing bug fixes",
      "Limited visibility into user impact"
    ],
    solutions: [
      "Automated bug detection and categorization",
      "Severity scoring based on user impact",
      "Reproduction step extraction",
      "Cross-platform issue correlation"
    ],
    outcomes: [
      "50% faster bug detection",
      "30% reduction in critical issues",
      "Improved app stability ratings",
      "Better resource allocation for fixes"
    ],
    persona: "QA Engineer",
    industry: "Software Development"
  },
  {
    icon: TrendingUp,
    title: "App Store Optimization",
    subtitle: "Improve app store rankings and conversions",
    description: "Optimize your app store presence using insights from user reviews and competitor analysis.",
    challenges: [
      "Low app store conversion rates",
      "Poor keyword optimization",
      "Unclear competitive positioning",
      "Limited understanding of user expectations"
    ],
    solutions: [
      "Review-based keyword extraction",
      "Competitor sentiment comparison",
      "User expectation analysis",
      "Rating improvement strategies"
    ],
    outcomes: [
      "35% increase in app store conversions",
      "Improved keyword rankings",
      "Better competitive positioning",
      "Higher overall app ratings"
    ],
    persona: "ASO Specialist",
    industry: "Mobile Apps"
  },
  {
    icon: Star,
    title: "Customer Success",
    subtitle: "Enhance user experience and retention",
    description: "Understand user satisfaction patterns and proactively address concerns to improve retention.",
    challenges: [
      "High user churn rates",
      "Unclear satisfaction drivers",
      "Reactive customer support",
      "Limited user feedback channels"
    ],
    solutions: [
      "Satisfaction trend monitoring",
      "Churn prediction indicators",
      "Proactive issue identification",
      "User sentiment tracking"
    ],
    outcomes: [
      "20% reduction in churn rate",
      "Improved customer satisfaction scores",
      "Proactive issue resolution",
      "Better user onboarding experiences"
    ],
    persona: "Customer Success Manager",
    industry: "SaaS & Mobile Apps"
  },
  {
    icon: Target,
    title: "Marketing Intelligence",
    subtitle: "Understand your audience and messaging",
    description: "Gain deep insights into user preferences, language, and motivations for better marketing campaigns.",
    challenges: [
      "Unclear target audience preferences",
      "Ineffective marketing messaging",
      "Limited user persona insights",
      "Poor campaign performance"
    ],
    solutions: [
      "User language and preference analysis",
      "Persona development from reviews",
      "Messaging effectiveness insights",
      "Competitive messaging analysis"
    ],
    outcomes: [
      "30% improvement in campaign performance",
      "Better audience targeting",
      "More effective messaging",
      "Improved brand positioning"
    ],
    persona: "Marketing Manager",
    industry: "All Industries"
  },
  {
    icon: Briefcase,
    title: "Competitive Intelligence",
    subtitle: "Stay ahead of the competition",
    description: "Monitor competitor performance, identify market opportunities, and benchmark your app's success.",
    challenges: [
      "Limited competitor insights",
      "Unclear market positioning",
      "Missing market opportunities",
      "Reactive competitive strategy"
    ],
    solutions: [
      "Competitor sentiment analysis",
      "Feature gap identification",
      "Market trend detection",
      "Benchmarking and positioning insights"
    ],
    outcomes: [
      "Better competitive positioning",
      "Faster market opportunity identification",
      "Improved feature differentiation",
      "Data-driven competitive strategy"
    ],
    persona: "Business Analyst",
    industry: "All Industries"
  }
]

const industries = [
  {
    name: "Gaming",
    description: "Understand player feedback, game balance issues, and monetization sentiment",
    examples: ["Player retention analysis", "In-app purchase feedback", "Game balance insights"]
  },
  {
    name: "E-commerce",
    description: "Optimize shopping experience, payment flows, and customer satisfaction",
    examples: ["Checkout process feedback", "Product discovery insights", "Customer service quality"]
  },
  {
    name: "Social Media",
    description: "Monitor community sentiment, feature adoption, and user safety concerns",
    examples: ["Feature adoption rates", "Community guidelines effectiveness", "User safety feedback"]
  },
  {
    name: "Finance",
    description: "Ensure security confidence, regulatory compliance, and user trust",
    examples: ["Security concern analysis", "Feature usability feedback", "Trust and reliability insights"]
  },
  {
    name: "Health & Fitness",
    description: "Track user engagement, health outcomes, and app effectiveness",
    examples: ["User motivation tracking", "Feature effectiveness analysis", "Health outcome correlations"]
  },
  {
    name: "Education",
    description: "Improve learning outcomes, engagement, and educational effectiveness",
    examples: ["Learning effectiveness feedback", "User engagement patterns", "Educational content quality"]
  }
]

export const UseCasesPage: React.FC = () => {
  const [selectedUseCase, setSelectedUseCase] = React.useState<number | null>(null)

  return (
    <>
      <SEOHead {...seoConfig.useCases} />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-white bg-white/10 hover:bg-white/20 font-medium transition-colors rounded-lg backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center justify-center mb-6">
                <Target className="h-8 w-8 text-[#2DD4BF] mr-3" />
                <h1 className="text-4xl md:text-5xl font-bold text-white">
                  Use <span className="text-[#2DD4BF]">Cases</span>
                </h1>
              </div>
              <p className="text-xl text-white/70 max-w-3xl mx-auto mb-8">
                Discover how teams across different roles and industries use AppReview.Today
                to transform user feedback into actionable insights and business growth.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Use Cases Grid */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {useCases.map((useCase, index) => (
                <motion.div
                  key={useCase.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                >
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-3 bg-[#2DD4BF]/20 rounded-lg mr-4">
                        <useCase.icon className="h-6 w-6 text-[#2DD4BF]" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-white">{useCase.title}</h3>
                        <p className="text-[#2DD4BF] font-medium">{useCase.subtitle}</p>
                        <div className="flex gap-2 mt-1">
                          <span className="text-xs bg-[#2DD4BF]/20 text-[#2DD4BF] px-2 py-1 rounded">{useCase.persona}</span>
                          <span className="text-xs bg-white/20 text-white/70 px-2 py-1 rounded">{useCase.industry}</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-white/70 mb-6">{useCase.description}</p>

                    <button
                      onClick={() => setSelectedUseCase(selectedUseCase === index ? null : index)}
                      className="flex items-center text-[#2DD4BF] hover:text-[#14B8A6] font-medium transition-colors"
                    >
                      {selectedUseCase === index ? 'Hide Details' : 'View Details'}
                      <ArrowRight className={`h-4 w-4 ml-2 transition-transform ${selectedUseCase === index ? 'rotate-90' : ''}`} />
                    </button>
                  </div>
                  
                  {selectedUseCase === index && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="border-t border-white/10 p-6 bg-white/5"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                          <h4 className="font-semibold text-red-400 mb-3 flex items-center">
                            <Shield className="h-4 w-4 mr-2" />
                            Challenges
                          </h4>
                          <ul className="space-y-2">
                            {useCase.challenges.map((challenge, idx) => (
                              <li key={idx} className="text-sm text-white/70 flex items-start">
                                <span className="w-2 h-2 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                {challenge}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold text-blue-400 mb-3 flex items-center">
                            <Zap className="h-4 w-4 mr-2" />
                            Solutions
                          </h4>
                          <ul className="space-y-2">
                            {useCase.solutions.map((solution, idx) => (
                              <li key={idx} className="text-sm text-white/70 flex items-start">
                                <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                {solution}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold text-[#2DD4BF] mb-3 flex items-center">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Outcomes
                          </h4>
                          <ul className="space-y-2">
                            {useCase.outcomes.map((outcome, idx) => (
                              <li key={idx} className="text-sm text-white/70 flex items-start">
                                <span className="w-2 h-2 bg-[#2DD4BF] rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                {outcome}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Industry Examples */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Industry-Specific Applications
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                See how different industries leverage AppReview.Today for their unique challenges and opportunities.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {industries.map((industry, index) => (
                <motion.div
                  key={industry.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl shadow-md hover:shadow-lg transition-shadow p-6"
                >
                  <h3 className="text-xl font-semibold text-white mb-3">{industry.name}</h3>
                  <p className="text-white/70 mb-4">{industry.description}</p>
                  <div className="space-y-2">
                    {industry.examples.map((example, idx) => (
                      <div key={idx} className="flex items-center text-sm text-white/70">
                        <BarChart3 className="h-4 w-4 text-[#2DD4BF] mr-2 flex-shrink-0" />
                        {example}
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Transform Your User Feedback?
              </h2>
              <p className="text-xl text-white/70 mb-8">
                Join thousands of teams who use AppReview.Today to make data-driven decisions and improve their apps.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => window.location.href = '/pricing'}
                  className="px-8 py-3 bg-[#2DD4BF] text-[#0A1128] rounded-lg font-semibold hover:bg-[#14B8A6] transition-colors"
                >
                  Start Free Trial
                </button>
                <button
                  onClick={() => window.location.href = '/how-it-works'}
                  className="px-8 py-3 bg-transparent border-2 border-white/20 text-white rounded-lg font-semibold hover:bg-white/10 transition-colors backdrop-blur-sm"
                >
                  Learn How It Works
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>

      <Footer />
    </>
  )
}
