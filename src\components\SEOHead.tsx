import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  noindex?: boolean;
  canonical?: string;
  structuredData?: object;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'AppReview.Today - AI-Powered User Review Analysis',
  description = 'Generate comprehensive reports from user reviews across App Store, Google Play, Reddit, and more. Get actionable insights in minutes.',
  keywords = 'app review analysis, user feedback analysis, app store optimization, mobile app insights, review sentiment analysis',
  image = '/app-review-today-192.png',
  url = 'https://appreview.today',
  type = 'website',
  noindex = false,
  canonical,
  structuredData
}) => {
  // 基础结构化数据 - Organization
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AppReview.Today",
    "description": "AI-powered app review analysis platform",
    "url": "https://appreview.today",
    "logo": {
      "@type": "ImageObject",
      "url": "https://appreview.today/app-review-today.svg"
    },
    "sameAs": [
      "https://github.com/happynocode/app-review-analysis"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": "https://appreview.today"
    }
  };

  // WebApplication 结构化数据
  const webApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AppReview.Today",
    "description": "AI-powered app review analysis platform that helps developers understand user feedback",
    "url": "https://appreview.today",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Free tier available with paid plans for advanced features"
    },
    "featureList": [
      "Multi-platform review scraping",
      "AI-powered sentiment analysis", 
      "Automated report generation",
      "Real-time monitoring",
      "PDF export functionality"
    ],
    "screenshot": {
      "@type": "ImageObject",
      "url": "https://appreview.today/app-review-today-192.png"
    }
  };

  // SoftwareApplication 结构化数据
  const softwareApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "AppReview.Today",
    "description": "Intelligent app review analysis platform leveraging AI technology",
    "url": "https://appreview.today",
    "applicationCategory": "DeveloperApplication",
    "operatingSystem": "Web",
    "softwareVersion": "2.0.0",
    "datePublished": "2024-12-01",
    "dateModified": "2025-01-01",
    "author": {
      "@type": "Organization",
      "name": "AppReview.Today Team"
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    }
  };

  // 合并所有结构化数据
  const allStructuredData = [
    organizationSchema,
    webApplicationSchema,
    softwareApplicationSchema
  ];

  if (structuredData) {
    allStructuredData.push(structuredData);
  }

  return (
    <Helmet>
      {/* 基础 Meta 标签 */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Robots */}
      {noindex && <meta name="robots" content="noindex,nofollow" />}
      
      {/* Open Graph 标签 */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content="AppReview.Today" />
      
      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* 额外的 Meta 标签 */}
      <meta name="author" content="AppReview.Today Team" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Language" content="en" />
      
      {/* 结构化数据 */}
      {allStructuredData.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
    </Helmet>
  );
};
