import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Send, User, Mail, Globe } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { supabase } from '../../lib/supabase';

interface CommentFormProps {
  postId: string;
  parentId?: string;
  onCommentSubmitted?: () => void;
  onCancel?: () => void;
  isReply?: boolean;
}

export const CommentForm: React.FC<CommentFormProps> = ({
  postId,
  parentId,
  onCommentSubmitted,
  onCancel,
  isReply = false
}) => {
  const [formData, setFormData] = useState({
    author_name: '',
    author_email: '',
    author_website: '',
    content: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const { error: submitError } = await supabase
        .from('blog_comments')
        .insert({
          post_id: postId,
          parent_id: parentId || null,
          author_name: formData.author_name,
          author_email: formData.author_email,
          author_website: formData.author_website || null,
          content: formData.content
        });

      if (submitError) throw submitError;

      setSuccess(true);
      setFormData({
        author_name: '',
        author_email: '',
        author_website: '',
        content: ''
      });

      if (onCommentSubmitted) {
        onCommentSubmitted();
      }

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-6 bg-green-500/10 border border-green-500/20 rounded-lg"
      >
        <p className="text-green-400 text-center">
          Thank you for your comment! It will be reviewed and published soon.
        </p>
      </motion.div>
    );
  }

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold text-white mb-4">
        {isReply ? 'Reply to Comment' : 'Leave a Comment'}
      </h3>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="author_name" className="block text-sm font-medium text-white/70 mb-2">
              <User className="w-4 h-4 inline mr-1" />
              Name *
            </label>
            <input
              type="text"
              id="author_name"
              name="author_name"
              value={formData.author_name}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#2DD4BF] focus:border-transparent"
              placeholder="Your name"
            />
          </div>

          <div>
            <label htmlFor="author_email" className="block text-sm font-medium text-white/70 mb-2">
              <Mail className="w-4 h-4 inline mr-1" />
              Email *
            </label>
            <input
              type="email"
              id="author_email"
              name="author_email"
              value={formData.author_email}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#2DD4BF] focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <label htmlFor="author_website" className="block text-sm font-medium text-white/70 mb-2">
            <Globe className="w-4 h-4 inline mr-1" />
            Website (optional)
          </label>
          <input
            type="url"
            id="author_website"
            name="author_website"
            value={formData.author_website}
            onChange={handleChange}
            className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#2DD4BF] focus:border-transparent"
            placeholder="https://yourwebsite.com"
          />
        </div>

        <div>
          <label htmlFor="content" className="block text-sm font-medium text-white/70 mb-2">
            Comment *
          </label>
          <textarea
            id="content"
            name="content"
            value={formData.content}
            onChange={handleChange}
            required
            rows={4}
            className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#2DD4BF] focus:border-transparent resize-vertical"
            placeholder="Share your thoughts..."
          />
        </div>

        {error && (
          <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div className="flex items-center justify-between">
          <p className="text-xs text-white/50">
            Your email will not be published. All comments are moderated.
          </p>
          
          <div className="flex space-x-3">
            {isReply && onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            
            <Button
              type="submit"
              disabled={isSubmitting}
              icon={Send}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Comment'}
            </Button>
          </div>
        </div>
      </form>
    </Card>
  );
};
