import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Check<PERSON>ircle, AlertCircle, Clock, X, CreditCard } from 'lucide-react'
import { useAuthStore } from '../stores/authStore'
import { getLatestPaymentStatus } from '../lib/database'

interface PaymentStatus {
  payment_status: string
  event_type: string
  plan_name: string | null
  reports_purchased: number | null
  error_message: string | null
  created_at: string
}

export const PaymentStatusBanner: React.FC = () => {
  const { user } = useAuthStore()
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (user) {
      checkLatestPaymentStatus()
    }
  }, [user])

  // Auto-hide successful payment status after 10 seconds
  useEffect(() => {
    if (paymentStatus && paymentStatus.payment_status === 'succeeded' && isVisible) {
      const timer = setTimeout(() => {
        if (paymentStatus) {
          markAsDismissed(paymentStatus)
        }
        setIsVisible(false)
      }, 10000) // 10 seconds

      return () => clearTimeout(timer)
    }
  }, [paymentStatus, isVisible])

  // Cleanup expired localStorage records
  useEffect(() => {
    if (user) {
      const cleanupExpiredRecords = () => {
        const keys = Object.keys(localStorage)
        const now = Date.now()
        
        keys.forEach(key => {
          if (key.startsWith(`payment_status_dismissed_${user.id}_`)) {
            const timestamp = key.split('_').pop()
            if (timestamp) {
              const recordTime = new Date(timestamp).getTime()
              // Remove records older than 1 hour
              if (now - recordTime > 60 * 60 * 1000) {
                localStorage.removeItem(key)
              }
            }
          }
        })
      }

      // Clean up on component mount
      cleanupExpiredRecords()
      
      // Clean up every hour
      const interval = setInterval(cleanupExpiredRecords, 60 * 60 * 1000)
      
      return () => clearInterval(interval)
    }
  }, [user])

  const getDismissedKey = (status: PaymentStatus) => {
    return `payment_status_dismissed_${user?.id}_${status.created_at}`
  }

  const isDismissed = (status: PaymentStatus) => {
    if (!user) return false
    const dismissedKey = getDismissedKey(status)
    return localStorage.getItem(dismissedKey) === 'true'
  }

  const markAsDismissed = (status: PaymentStatus) => {
    if (!user) return
    const dismissedKey = getDismissedKey(status)
    localStorage.setItem(dismissedKey, 'true')
  }

  const checkLatestPaymentStatus = async () => {
    if (!user) return

    setIsLoading(true)
    try {
      const status = await getLatestPaymentStatus(user.id)
      
      if (status) {
        // Only show banner for recent status updates (within 5 minutes) and not dismissed
        const statusAge = Date.now() - new Date(status.created_at).getTime()
        if (statusAge < 5 * 60 * 1000 && !isDismissed(status)) { // 5 minutes
          setPaymentStatus(status)
          setIsVisible(true)
        }
      }
    } catch (error) {
      console.debug('Payment status check failed (this is normal for new users):', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'created':
        return {
          icon: CreditCard,
          color: 'bg-blue-500/20 border-blue-500/30 text-blue-400',
          iconColor: 'text-blue-400',
          title: '支付已创建',
          message: '您的支付正在初始化...'
        }
      case 'processing':
        return {
          icon: Clock,
          color: 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400',
          iconColor: 'text-yellow-400',
          title: '支付处理中',
          message: '正在处理您的支付，请稍候...'
        }
      case 'succeeded':
        return {
          icon: CheckCircle,
          color: 'bg-green-500/20 border-green-500/30 text-green-400',
          iconColor: 'text-green-400',
          title: '支付成功！',
          message: paymentStatus?.reports_purchased 
            ? `成功购买 ${paymentStatus.reports_purchased} 个报告额度`
            : '支付已完成'
        }
      case 'failed':
        return {
          icon: AlertCircle,
          color: 'bg-red-500/20 border-red-500/30 text-red-400',
          iconColor: 'text-red-400',
          title: '支付失败',
          message: paymentStatus?.error_message || '支付过程中出现错误，请重试'
        }
      case 'canceled':
        return {
          icon: X,
          color: 'bg-gray-500/20 border-gray-500/30 text-gray-400',
          iconColor: 'text-gray-400',
          title: '支付已取消',
          message: '您已取消此次支付'
        }
      case 'expired':
        return {
          icon: Clock,
          color: 'bg-orange-500/20 border-orange-500/30 text-orange-400',
          iconColor: 'text-orange-400',
          title: '支付会话已过期',
          message: '请重新开始支付流程'
        }
      default:
        return {
          icon: AlertCircle,
          color: 'bg-gray-500/20 border-gray-500/30 text-gray-400',
          iconColor: 'text-gray-400',
          title: '未知状态',
          message: '支付状态未知'
        }
    }
  }

  const handleDismiss = () => {
    if (paymentStatus) {
      markAsDismissed(paymentStatus)
    }
    setIsVisible(false)
  }

  if (!user || !paymentStatus || !isVisible) {
    return null
  }

  const statusConfig = getStatusConfig(paymentStatus.payment_status)
  const StatusIcon = statusConfig.icon

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
          className="fixed top-0 left-0 right-0 z-50 p-4"
        >
          <div className="max-w-4xl mx-auto">
            <div className={`rounded-lg border p-4 backdrop-blur-sm ${statusConfig.color}`}>
              <div className="flex items-start space-x-3">
                <StatusIcon className={`w-6 h-6 mt-0.5 ${statusConfig.iconColor}`} />
                
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm">{statusConfig.title}</h3>
                  <p className="text-sm opacity-90 mt-1">{statusConfig.message}</p>
                  
                  {paymentStatus.plan_name && (
                    <p className="text-xs opacity-70 mt-1">
                      计划: {paymentStatus.plan_name}
                    </p>
                  )}
                </div>

                <button
                  onClick={handleDismiss}
                  className="p-1 hover:bg-white/10 rounded transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
} 