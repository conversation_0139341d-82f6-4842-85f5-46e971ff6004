import React from 'react';
import { motion } from 'framer-motion';
import { Megaphone, TrendingUp, Users, MessageSquare, Star, Target, CheckCircle, ArrowRight, BookOpen, ExternalLink } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const ForMarketersPage: React.FC = () => {
  const features = [
    {
      icon: <Megaphone className="w-8 h-8 text-pink-600" />,
      title: "Brand Sentiment Analysis",
      description: "Monitor brand perception across app stores. Track sentiment trends and identify reputation risks before they escalate."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-green-600" />,
      title: "Campaign Performance Tracking",
      description: "Measure how marketing campaigns impact user sentiment and app store ratings. Optimize messaging for better results."
    },
    {
      icon: <Users className="w-8 h-8 text-blue-600" />,
      title: "Audience Insights",
      description: "Understand your users' language, preferences, and pain points. Create more targeted and effective marketing messages."
    },
    {
      icon: <MessageSquare className="w-8 h-8 text-purple-600" />,
      title: "Content Optimization",
      description: "Identify trending topics and user interests from reviews. Create content that resonates with your audience."
    }
  ];

  const campaigns = [
    {
      title: "App Store Optimization",
      description: "Optimize app store listings based on user feedback patterns",
      tactics: ["Keyword optimization from reviews", "Feature highlighting", "Screenshot optimization", "Description refinement"],
      result: "45% increase in conversion rate"
    },
    {
      title: "Social Media Strategy",
      description: "Create authentic social content based on real user experiences",
      tactics: ["User story amplification", "Feature showcases", "Problem-solution content", "Community building"],
      result: "3x higher engagement rate"
    },
    {
      title: "Influencer Partnerships",
      description: "Identify and partner with users who love your app",
      tactics: ["Power user identification", "Authentic testimonials", "Case study development", "Referral programs"],
      result: "60% more qualified leads"
    }
  ];

  const metrics = [
    { value: "85%", label: "Better Campaign ROI", description: "Data-driven marketing decisions" },
    { value: "2.8x", label: "Higher Engagement", description: "Authentic user-focused content" },
    { value: "67%", label: "Improved Brand Sentiment", description: "Proactive reputation management" },
    { value: "40%", label: "Faster Content Creation", description: "User insight-driven content" }
  ];

  const tools = [
    {
      name: "Sentiment Dashboard",
      description: "Real-time brand sentiment monitoring across all app stores",
      features: ["Sentiment trends", "Alert system", "Competitor comparison", "Historical analysis"]
    },
    {
      name: "Content Generator",
      description: "AI-powered content suggestions based on user feedback",
      features: ["Topic suggestions", "Messaging optimization", "Hashtag recommendations", "Content calendar"]
    },
    {
      name: "Campaign Tracker",
      description: "Track marketing campaign impact on user sentiment",
      features: ["Before/after analysis", "Attribution tracking", "ROI measurement", "A/B testing"]
    }
  ];

  const testimonials = [
    {
      quote: "AppReview.Today helped us identify the exact language our users love. Our app store conversion rate increased by 45% after optimizing based on their insights.",
      author: "Jessica Park",
      role: "Growth Marketing Manager",
      company: "FitTracker Pro",
      avatar: "/images/testimonials/jessica.jpg"
    },
    {
      quote: "We discovered our users' biggest pain points and created a campaign around solving them. It became our most successful campaign ever.",
      author: "David Kim",
      role: "Marketing Director",
      company: "StudyBuddy",
      avatar: "/images/testimonials/david.jpg"
    }
  ];

  const blogArticles = [
    {
      title: "App Store Marketing Insights from Reviews",
      excerpt: "Discover how to extract powerful marketing insights from app reviews to improve your App Store Optimization (ASO) strategy and user acquisition.",
      slug: "app-store-marketing-insights-reviews",
      readTime: "14 min read",
      category: "ASO Strategy"
    },
    {
      title: "User Persona Development Through Review Analysis",
      excerpt: "Discover how to create accurate, data-driven user personas using authentic feedback from app reviews and user-generated content.",
      slug: "user-persona-development-review-analysis",
      readTime: "18 min read",
      category: "User Research"
    },
    {
      title: "Content Marketing Ideas from User Feedback",
      excerpt: "Transform app reviews into powerful content marketing strategies. Learn how to extract content ideas, messaging insights, and storytelling opportunities from user feedback.",
      slug: "content-marketing-ideas-user-feedback",
      readTime: "17 min read",
      category: "Content Strategy"
    },
    {
      title: "Influencer Collaboration Based on Review Trends",
      excerpt: "Discover how to identify and collaborate with influencers using app review data. Learn to find authentic voices, measure impact, and build successful partnerships.",
      slug: "influencer-collaboration-based-review-trends",
      readTime: "20 min read",
      category: "Influencer Marketing"
    },
    {
      title: "Social Media Strategy from Review Sentiment",
      excerpt: "Transform app review sentiment into powerful social media strategies. Learn to create authentic content, manage reputation, and engage communities based on user feedback.",
      slug: "social-media-strategy-review-sentiment",
      readTime: "22 min read",
      category: "Social Media"
    }
  ];

  return (
    <>
      <SEOHead 
        title="For Marketers - AppReview.Today | Brand Sentiment & Campaign Analytics"
        description="Transform user feedback into marketing gold. Brand sentiment analysis, campaign performance tracking, audience insights, and content optimization for marketing teams."
        canonical="/for-marketers"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "AppReview.Today for Marketers",
          "description": "Marketing analytics platform designed for digital marketers and growth teams",
          "audience": {
            "@type": "Audience",
            "audienceType": "Marketers"
          }
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-white bg-white/10 hover:bg-white/20 font-medium transition-colors rounded-lg backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex justify-center mb-6">
                <Megaphone className="w-16 h-16 text-[#2DD4BF]" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
                Marketing Intelligence for <span className="text-[#2DD4BF]">Growth Teams</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-white/70">
                Turn user feedback into marketing gold. Monitor brand sentiment, optimize campaigns,
                and create content that truly resonates with your audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-[#0A1128] px-8 py-3 rounded-lg font-semibold transition-colors">
                  Start Marketing Analysis
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View Success Stories
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Marketing Features */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Marketing Superpowers
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Everything you need to create data-driven marketing campaigns that convert
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-white/70">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Campaign Strategies */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Proven Campaign Strategies
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Learn how successful marketers use user feedback to drive growth
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {campaigns.map((campaign, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl"
                >
                  <h3 className="text-xl font-bold text-white mb-3">
                    {campaign.title}
                  </h3>
                  <p className="text-white/70 mb-6">
                    {campaign.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {campaign.tactics.map((tactic, tacticIndex) => (
                      <div key={tacticIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-[#2DD4BF] flex-shrink-0" />
                        <span className="text-white/70">{tactic}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-[#2DD4BF]/20 p-4 rounded-lg border border-[#2DD4BF]/30">
                    <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold">
                      <TrendingUp className="w-4 h-4" />
                      {campaign.result}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Marketing Tools */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Marketing Tools & Features
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Comprehensive toolkit for modern marketing teams
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {tools.map((tool, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg"
                >
                  <h3 className="text-xl font-bold text-white mb-3">
                    {tool.name}
                  </h3>
                  <p className="text-white/70 mb-6">
                    {tool.description}
                  </p>
                  <div className="space-y-2">
                    {tool.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-[#2DD4BF] rounded-full"></div>
                        <span className="text-white/70">{feature}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Metrics */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Marketing Results That Matter
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                See how marketing teams are achieving better ROI with user feedback insights
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl text-center"
                >
                  <div className="text-4xl font-bold text-[#2DD4BF] mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">
                    {metric.label}
                  </div>
                  <div className="text-white/70 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Loved by Marketing Teams
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg"
                >
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-[#2DD4BF] fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-white/70 mb-6 text-lg">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center gap-4">
                    <LazyImage
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-12 h-12 rounded-full"
                    />
                    <div>
                      <div className="font-semibold text-white">{testimonial.author}</div>
                      <div className="text-white/70">{testimonial.role}, {testimonial.company}</div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Expert Resources */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <div className="flex justify-center mb-4">
                <BookOpen className="w-12 h-12 text-[#2DD4BF]" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Expert Marketing Resources
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Advanced strategies and insights to master review-driven marketing
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {blogArticles.map((article, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow group cursor-pointer"
                  onClick={() => window.open(`/blog/${article.slug}`, '_blank')}
                >
                  <div className="flex items-start justify-between mb-4">
                    <span className="bg-[#2DD4BF]/20 text-[#2DD4BF] px-3 py-1 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                    <span className="text-white/50 text-sm">{article.readTime}</span>
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-[#2DD4BF] transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-white/70 mb-4 text-lg">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center text-[#2DD4BF] font-semibold group-hover:text-[#14B8A6] transition-colors">
                    <span>Read Full Guide</span>
                    <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Supercharge Your Marketing?
              </h2>
              <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
                Join thousands of marketers who are creating better campaigns with user feedback insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] text-[#0A1128] hover:bg-[#14B8A6] px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Start Free Campaign Analysis
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  Book Strategy Session
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>

      <Footer />
    </>
  );
};
