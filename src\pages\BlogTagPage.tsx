import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, Clock, Tag, ArrowLeft } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { LoadingSpinner } from '../components/LoadingSpinner';
import { supabase } from '../lib/supabase';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image_url: string;
  author: {
    name: string;
    avatar_url: string;
  };
  category: {
    name: string;
    slug: string;
    color: string;
  };
  reading_time_minutes: number;
  published_at: string;
  view_count: number;
}

interface BlogTag {
  id: string;
  name: string;
  slug: string;
  description: string;
  usage_count: number;
}

export const BlogTagPage: React.FC = () => {
  const { tagSlug } = useParams<{ tagSlug: string }>();
  const [tag, setTag] = useState<BlogTag | null>(null);
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 9;

  useEffect(() => {
    if (tagSlug) {
      fetchTagData(tagSlug);
    }
  }, [tagSlug]);

  const fetchTagData = async (slug: string) => {
    try {
      setLoading(true);
      setError(null);

      // Fetch tag
      const { data: tagData, error: tagError } = await supabase
        .from('blog_tags')
        .select('*')
        .eq('slug', slug)
        .single();

      if (tagError) {
        setError('Tag not found');
        return;
      }

      setTag(tagData);

      // Fetch posts with this tag
      const { data: postsData } = await supabase
        .from('blog_posts')
        .select(`
          *,
          author:blog_authors(name, avatar_url),
          category:blog_categories(name, slug, color)
        `)
        .eq('status', 'published')
        .lte('published_at', new Date().toISOString())
        .order('published_at', { ascending: false });

      if (postsData) {
        // Filter posts that have this tag
        const { data: postTagsData } = await supabase
          .from('blog_post_tags')
          .select('post_id')
          .eq('tag_id', tagData.id);

        if (postTagsData) {
          const postIds = postTagsData.map(pt => pt.post_id);
          const filteredPosts = postsData.filter(post => postIds.includes(post.id));
          setPosts(filteredPosts);
        }
      }
    } catch (error) {
      console.error('Error fetching tag data:', error);
      setError('Failed to load tag');
    } finally {
      setLoading(false);
    }
  };

  const totalPages = Math.ceil(posts.length / postsPerPage);
  const currentPosts = posts.slice(
    (currentPage - 1) * postsPerPage,
    currentPage * postsPerPage
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !tag) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Tag Not Found</h1>
          <p className="text-gray-600 mb-8">The tag you're looking for doesn't exist.</p>
          <Link
            to="/blog"
            className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHead 
        title={`${tag.name} Articles | AppReview.Today Blog`}
        description={`Explore articles tagged with ${tag.name}. ${tag.description || `Find insights and tutorials related to ${tag.name}.`}`}
        canonical={`/blog/tag/${tag.slug}`}
      />
      
      <div className="min-h-screen bg-gray-50">
        {/* Navigation */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Link
              to="/blog"
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
          </div>
        </div>

        {/* Tag Header */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <div className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-700 text-lg font-medium rounded-full mb-6">
                <Tag className="w-5 h-5" />
                {tag.name}
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Articles tagged with "{tag.name}"
              </h1>
              {tag.description && (
                <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                  {tag.description}
                </p>
              )}
              <div className="text-gray-500">
                {posts.length} article{posts.length !== 1 ? 's' : ''} with this tag
              </div>
            </motion.div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {currentPosts.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No articles found with this tag yet.</p>
                <Link
                  to="/blog"
                  className="inline-block mt-4 text-blue-600 hover:text-blue-700 transition-colors"
                >
                  Browse all articles
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {currentPosts.map((post, index) => (
                  <motion.article
                    key={post.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                  >
                    {/* Content */}
                    <div className="p-6">
                      {/* Category */}
                      {post.category && (
                        <div className="mb-4">
                          <span
                            className="inline-block px-3 py-1 rounded-full text-white text-sm font-medium"
                            style={{ backgroundColor: post.category.color }}
                          >
                            {post.category.name}
                          </span>
                        </div>
                      )}

                      <h2 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                        <Link to={`/blog/${post.slug}`}>
                          {post.title}
                        </Link>
                      </h2>

                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {post.excerpt}
                      </p>

                      {/* Meta Information */}
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(post.published_at)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{post.reading_time_minutes} min read</span>
                        </div>
                      </div>
                    </div>
                  </motion.article>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-12">
                <div className="flex gap-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-4 py-2 rounded-lg transition-colors ${
                        currentPage === page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </section>
      </div>
    </>
  );
};
