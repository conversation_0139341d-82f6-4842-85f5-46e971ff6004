# Stripe支付集成设置指南

## 前置条件

1. 注册 [Stripe 账户](https://stripe.com)
2. 获取 Stripe API 密钥
3. 配置 Stripe Webhook

## 步骤1: 获取 Stripe API 密钥

1. 登录 Stripe Dashboard
2. 进入 **Developers > API Keys**
3. 复制以下密钥：
   - **Publishable key** (pk_test_...)
   - **Secret key** (sk_test_...)

## 步骤2: 创建产品和价格

在 Stripe Dashboard 中创建以下产品：

### Starter Plan
- 产品名称: "Starter Plan"
- 价格: $4.99 USD
- 类型: 一次性付款
- 复制 Price ID (price_1RpZ1PJ190Ki7I11Zt5E7Fzk)

### Professional Plan  
- 产品名称: "Professional Plan"
- 价格: $9.99 USD
- 类型: 一次性付款
- 复制 Price ID (price_1RpZ1fJ190Ki7I11t0JGguCe)

### Business Plan
- 产品名称: "Business Plan"  
- 价格: $19.99 USD
- 类型: 一次性付款
- 复制 Price ID (price_1RpZ1mJ190Ki7I11KttjsEkQ)

## 步骤3: 更新价格 ID

在 `src/lib/stripe.ts` 文件中，更新 `pricingPlans` 数组中的 `priceId` 字段：

```typescript
export const pricingPlans: PricingPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    price: 499,
    reports: 3,
    priceId: 'price_YOUR_ACTUAL_STARTER_PRICE_ID' // 替换为实际的 Price ID
  },
  // ... 其他计划
]
```

## 步骤4: 配置环境变量

### 前端配置
在项目根目录创建 `.env` 文件：

```bash
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key
```

### 后端配置 (Supabase Edge Functions)
在 Supabase Dashboard > Settings > Edge Functions 中添加以下密钥：

```bash
STRIPE_SECRET_KEY=sk_test_your_actual_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## 步骤5: 设置 Webhook

1. 在 Stripe Dashboard 进入 **Developers > Webhooks**
2. 点击 **Add endpoint**
3. 设置 Endpoint URL: `https://mihmdokivbllrcrjoojo.supabase.co/functions/v1/stripe-webhook`
4. 选择监听以下事件：
   - `checkout.session.completed` ✅ 必需：支付成功处理
   - `checkout.session.expired` ⚠️ 可选：会话过期清理
   - `payment_intent.created` 📊 反馈：支付创建
   - `payment_intent.processing` 📊 反馈：支付处理中
   - `payment_intent.succeeded` 📊 反馈：支付成功
   - `payment_intent.payment_failed` 📊 反馈：支付失败
   - `payment_intent.canceled` 📊 反馈：支付取消
5. 复制 Webhook Secret (whsec_...)

## 步骤6: 运行数据库迁移

首先需要创建支付日志表：

```bash
# 在 Supabase SQL Editor 中运行 supabase/migrations/001_add_payment_logs.sql
# 或者通过 Supabase CLI
supabase db reset
```

## 步骤7: 部署 Edge Functions

```bash
# 部署 Stripe 相关的 Edge Functions
supabase functions deploy create-checkout-session
supabase functions deploy stripe-webhook
```

## 步骤8: 测试支付流程

1. 启动开发服务器
2. 登录应用
3. 进入 Pricing 页面
4. 选择计划并测试支付
5. 使用 Stripe 测试卡号: `4242 4242 4242 4242`

## 新功能特性

### 🔔 实时支付状态反馈

用户现在会看到详细的支付状态：
- **支付创建** - 支付意图已创建
- **支付处理中** - 正在处理支付
- **支付成功** - 支付完成，额度已添加
- **支付失败** - 显示具体错误信息
- **支付取消/过期** - 相应状态提示

### 📊 支付历史记录

在用户Profile页面显示：
- 所有支付事件的详细日志
- 支付金额和购买的报告次数
- 失败原因和错误信息
- 支付时间戳

### 🎯 智能状态横幅

- 自动显示最近5分钟内的支付状态
- 成功状态自动隐藏
- 失败状态持续显示直到用户关闭
- 支持多种状态的可视化反馈

## 安全注意事项

1. 永远不要在前端代码中暴露 Secret Key
2. 使用 Webhook 验证支付状态
3. 在生产环境中使用 HTTPS
4. 定期轮换 API 密钥
5. 监控支付日志以发现异常行为

## 故障排除

### 常见问题

1. **支付失败**: 检查 Price ID 是否正确
2. **Webhook 失败**: 验证 Webhook Secret 配置
3. **用户限额未更新**: 检查 Webhook 日志和数据库权限
4. **支付状态不显示**: 检查数据库迁移是否成功执行

### 调试工具

- Stripe Dashboard > Logs
- Supabase Dashboard > Edge Functions > Logs
- Supabase Dashboard > Database > payment_logs 表
- 浏览器开发者工具

### 支付状态调试

查看 `payment_logs` 表来追踪支付流程：

```sql
SELECT 
  event_type,
  payment_status,
  plan_name,
  reports_purchased,
  error_message,
  created_at
FROM payment_logs 
WHERE user_id = 'your-user-id'
ORDER BY created_at DESC;
``` 