import React from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Shield, TrendingUp, Smartphone, Lock, CheckCircle, ArrowRight, Target } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const FintechAppsPage: React.FC = () => {
  const features = [
    {
      icon: <Shield className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Security & Trust Analysis",
      description: "Monitor user confidence in security features, fraud protection, and data privacy measures."
    },
    {
      icon: <CreditCard className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Payment Experience Insights",
      description: "Analyze feedback on payment flows, transaction speeds, and payment method preferences."
    },
    {
      icon: <Smartphone className="w-8 h-8 text-[#2DD4BF]" />,
      title: "User Interface Feedback",
      description: "Understand user experience with financial dashboards, navigation, and feature accessibility."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Feature Adoption Analysis",
      description: "Track user sentiment on new financial features, investment tools, and budgeting capabilities."
    }
  ];

  const fintechTypes = [
    {
      title: "Digital Banking",
      description: "Account management, transaction history, and customer service optimization",
      insights: ["Account setup experience", "Transaction categorization", "Customer support quality", "Mobile check deposit"],
      metrics: "78% improvement in user trust"
    },
    {
      title: "Investment Apps",
      description: "Portfolio management, trading experience, and educational content analysis",
      insights: ["Trading interface usability", "Portfolio visualization", "Market data accuracy", "Educational content quality"],
      metrics: "65% better user engagement"
    },
    {
      title: "Payment Solutions",
      description: "Transaction processing, security features, and merchant integration",
      insights: ["Payment speed feedback", "Security perception", "Merchant onboarding", "Fee transparency"],
      metrics: "82% faster transaction approval"
    },
    {
      title: "Personal Finance",
      description: "Budgeting tools, expense tracking, and financial goal management",
      insights: ["Budget creation ease", "Expense categorization", "Goal tracking accuracy", "Spending insights quality"],
      metrics: "71% better financial awareness"
    }
  ];

  const challenges = [
    {
      challenge: "User Trust & Security",
      solution: "Monitor security-related feedback to improve trust and confidence",
      impact: "85% increase in user trust scores"
    },
    {
      challenge: "Regulatory Compliance",
      solution: "Track user feedback on compliance features and documentation",
      impact: "95% compliance satisfaction rate"
    },
    {
      challenge: "Feature Complexity",
      solution: "Identify confusing features and optimize user experience",
      impact: "60% reduction in support tickets"
    },
    {
      challenge: "Onboarding Friction",
      solution: "Analyze onboarding feedback to streamline user acquisition",
      impact: "45% improvement in completion rates"
    }
  ];

  const metrics = [
    { value: "150M+", label: "Fintech Reviews Analyzed", description: "Comprehensive financial services dataset" },
    { value: "97%", label: "Security Sentiment Accuracy", description: "Fintech-specific trust analysis" },
    { value: "68%", label: "Faster Compliance Issues Detection", description: "Real-time regulatory feedback monitoring" },
    { value: "52%", label: "Better User Onboarding", description: "Data-driven UX optimization" }
  ];

  return (
    <>
      <SEOHead 
        title="Fintech Apps Analytics - AppReview.Today | Financial App User Experience"
        description="Optimize your fintech app with user feedback analysis. Improve security trust, streamline payments, enhance user experience and boost financial app ratings."
        canonical="/fintech-apps"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "Fintech Apps Analytics - AppReview.Today",
          "description": "User experience optimization platform for fintech and financial services mobile apps",
          "audience": {
            "@type": "Audience",
            "audienceType": "Fintech Developers"
          },
          "about": {
            "@type": "Thing",
            "name": "Fintech Analytics"
          }
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-white bg-white/10 hover:bg-white/20 font-medium transition-colors rounded-lg backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex justify-center mb-6">
                <Shield className="w-16 h-16 text-[#2DD4BF]" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
                Secure Your <span className="text-[#2DD4BF]">Fintech Success</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-white/70">
                Build trust through user feedback analysis. Optimize security, streamline payments,
                and create financial experiences users love and trust.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-[#0A1128] px-8 py-3 rounded-lg font-semibold transition-colors">
                  Analyze User Trust
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View Fintech Case Studies
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Fintech Features */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Fintech-Focused Analytics
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Specialized insights for financial technology and banking applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-white/70">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Fintech Categories */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Specialized for Every Fintech Vertical
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Tailored analytics for different types of financial technology applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {fintechTypes.map((type, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl"
                >
                  <h3 className="text-xl font-bold text-white mb-3">
                    {type.title}
                  </h3>
                  <p className="text-white/70 mb-6">
                    {type.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {type.insights.map((insight, insightIndex) => (
                      <div key={insightIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-[#2DD4BF] flex-shrink-0" />
                        <span className="text-white/70">{insight}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-[#2DD4BF]/20 p-4 rounded-lg border border-[#2DD4BF]/30">
                    <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold">
                      <TrendingUp className="w-4 h-4" />
                      {type.metrics}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Fintech Challenges */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Solve Fintech Challenges
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Address the critical challenges facing financial technology applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {challenges.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg"
                >
                  <div className="flex items-start gap-4 mb-4">
                    <Target className="w-6 h-6 text-red-400 flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">
                        Challenge: {item.challenge}
                      </h3>
                      <p className="text-white/70 mb-4">
                        {item.solution}
                      </p>
                      <div className="bg-[#2DD4BF]/20 p-3 rounded-lg border border-[#2DD4BF]/30">
                        <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold text-sm">
                          <TrendingUp className="w-4 h-4" />
                          {item.impact}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Fintech Metrics */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Proven Results for Fintech Apps
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                See how fintech companies are building trust and improving user experience
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl text-center"
                >
                  <div className="text-4xl font-bold text-[#2DD4BF] mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">
                    {metric.label}
                  </div>
                  <div className="text-white/70 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Build Financial Trust?
              </h2>
              <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
                Join leading fintech companies that are building user trust and improving financial experiences with data-driven insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] text-[#0A1128] hover:bg-[#14B8A6] px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Start Fintech Analysis
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View Fintech Demo
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
      
      <Footer />
    </>
  );
};
