import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, Clock, User, Tag, ArrowLeft, Share2, Eye } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { LoadingSpinner } from '../components/LoadingSpinner';
import { CommentList } from '../components/blog/CommentList';
import { supabase } from '../lib/supabase';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image_url: string;
  author: {
    name: string;
    avatar_url: string;
    bio: string;
    social_links: any;
  };
  category: {
    name: string;
    slug: string;
    color: string;
  };
  tags: Array<{
    name: string;
    slug: string;
  }>;
  reading_time_minutes: number;
  published_at: string;
  view_count: number;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
}

interface RelatedPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image_url: string;
  published_at: string;
  reading_time_minutes: number;
}

export const BlogPostPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<RelatedPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (slug) {
      fetchBlogPost(slug);
    }
  }, [slug]);

  const fetchBlogPost = async (postSlug: string) => {
    try {
      setLoading(true);
      setError(null);

      // Fetch the blog post
      const { data: postData, error: postError } = await supabase
        .from('blog_posts')
        .select(`
          *,
          author:blog_authors(name, avatar_url, bio, social_links),
          category:blog_categories(name, slug, color),
          tags:blog_post_tags(tag:blog_tags(name, slug))
        `)
        .eq('slug', postSlug)
        .eq('status', 'published')
        .lte('published_at', new Date().toISOString())
        .single();

      if (postError) {
        setError('Blog post not found');
        return;
      }

      if (postData) {
        const formattedPost = {
          ...postData,
          tags: postData.tags?.map((t: any) => t.tag) || []
        };
        setPost(formattedPost);

        // Update view count
        await supabase
          .from('blog_posts')
          .update({ view_count: (postData.view_count || 0) + 1 })
          .eq('id', postData.id);

        // Fetch related posts using the recommendations API
        try {
          const recommendationsResponse = await fetch(
            `${supabase.supabaseUrl}/functions/v1/blog-recommendations?post=${postSlug}&limit=3`,
            {
              headers: {
                'Authorization': `Bearer ${supabase.supabaseKey}`,
                'Content-Type': 'application/json'
              }
            }
          );

          if (recommendationsResponse.ok) {
            const recommendationsData = await recommendationsResponse.json();
            setRelatedPosts(recommendationsData.recommendations || []);
          } else {
            // Fallback to simple category-based related posts
            const { data: relatedData } = await supabase
              .from('blog_posts')
              .select('id, title, slug, excerpt, featured_image_url, published_at, reading_time_minutes')
              .eq('category_id', postData.category_id)
              .eq('status', 'published')
              .neq('id', postData.id)
              .lte('published_at', new Date().toISOString())
              .order('published_at', { ascending: false })
              .limit(3);

            if (relatedData) {
              setRelatedPosts(relatedData);
            }
          }
        } catch (recommendationError) {
          console.error('Error fetching recommendations:', recommendationError);
          // Fallback to simple category-based related posts
          if (postData.category) {
            const { data: relatedData } = await supabase
              .from('blog_posts')
              .select('id, title, slug, excerpt, featured_image_url, published_at, reading_time_minutes')
              .eq('category_id', postData.category_id)
              .eq('status', 'published')
              .neq('id', postData.id)
              .lte('published_at', new Date().toISOString())
              .order('published_at', { ascending: false })
              .limit(3);

            if (relatedData) {
              setRelatedPosts(relatedData);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error fetching blog post:', error);
      setError('Failed to load blog post');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const sharePost = () => {
    if (navigator.share && post) {
      navigator.share({
        title: post.title,
        text: post.excerpt,
        url: window.location.href,
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Blog Post Not Found</h1>
          <p className="text-gray-600 mb-8">The blog post you're looking for doesn't exist or has been removed.</p>
          <Link
            to="/blog"
            className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEOHead 
        title={post.seo_title || `${post.title} | AppReview.Today Blog`}
        description={post.seo_description || post.excerpt}
        canonical={`/blog/${post.slug}`}
        keywords={post.seo_keywords}
        structuredData={{
          "@context": "https://schema.org",
          "@type": "BlogPosting",
          "headline": post.title,
          "description": post.excerpt,
          "image": post.featured_image_url,
          "author": {
            "@type": "Person",
            "name": post.author.name
          },
          "publisher": {
            "@type": "Organization",
            "name": "AppReview.Today",
            "logo": {
              "@type": "ImageObject",
              "url": "https://appreview.today/logo.png"
            }
          },
          "datePublished": post.published_at,
          "dateModified": post.published_at,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `https://appreview.today/blog/${post.slug}`
          }
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Navigation */}
        <div className="bg-white/5 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Link
              to="/blog"
              className="inline-flex items-center gap-2 text-[#2DD4BF] hover:text-white transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
          </div>
        </div>

        {/* Article Header */}
        <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.header
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            {/* Category */}
            {post.category && (
              <div className="mb-4">
                <span 
                  className="inline-block px-3 py-1 rounded-full text-white text-sm font-medium"
                  style={{ backgroundColor: post.category.color }}
                >
                  {post.category.name}
                </span>
              </div>
            )}

            {/* Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
              {post.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-white/70 mb-6">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(post.published_at)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{post.reading_time_minutes} min read</span>
              </div>
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                <span>{post.view_count} views</span>
              </div>
              <button
                onClick={sharePost}
                className="flex items-center gap-2 text-[#2DD4BF] hover:text-white transition-colors"
              >
                <Share2 className="w-4 h-4" />
                Share
              </button>
            </div>

            {/* Author */}
            <div className="flex items-center gap-4 p-4 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg">
              <img
                src={post.author.avatar_url || '/images/default-avatar.jpg'}
                alt={post.author.name}
                className="w-12 h-12 rounded-full"
              />
              <div>
                <h3 className="font-semibold text-white">{post.author.name}</h3>
                <p className="text-white/70 text-sm">{post.author.bio}</p>
              </div>
            </div>
          </motion.header>

          {/* Featured Image */}
          {post.featured_image_url && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-8"
            >
              <LazyImage
                src={post.featured_image_url}
                alt={post.title}
                className="w-full h-64 md:h-96 object-cover rounded-xl"
              />
            </motion.div>
          )}

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="prose prose-lg prose-invert max-w-none mb-8"
            dangerouslySetInnerHTML={{ __html: post.content.replace(/\n/g, '<br>') }}
          />

          {/* Tags */}
          {post.tags.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mb-8"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map(tag => (
                  <Link
                    key={tag.slug}
                    to={`/blog/tag/${tag.slug}`}
                    className="inline-flex items-center gap-1 px-3 py-1 bg-white/10 text-white/80 text-sm rounded-full hover:bg-white/20 transition-colors"
                  >
                    <Tag className="w-3 h-3" />
                    {tag.name}
                  </Link>
                ))}
              </div>
            </motion.div>
          )}
        </article>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section className="bg-white/5 backdrop-blur-sm py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <h2 className="text-2xl font-bold text-white mb-8">Related Articles</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {relatedPosts.map(relatedPost => (
                  <motion.article
                    key={relatedPost.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden hover:bg-white/10 transition-all duration-300"
                  >
                    <LazyImage
                      src={relatedPost.featured_image_url || '/images/blog-placeholder.jpg'}
                      alt={relatedPost.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-6">
                      <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                        <Link
                          to={`/blog/${relatedPost.slug}`}
                          className="hover:text-[#2DD4BF] transition-colors"
                        >
                          {relatedPost.title}
                        </Link>
                      </h3>
                      <p className="text-white/70 text-sm mb-4 line-clamp-3">
                        {relatedPost.excerpt}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-white/50">
                        <span>{formatDate(relatedPost.published_at)}</span>
                        <span>{relatedPost.reading_time_minutes} min read</span>
                      </div>
                    </div>
                  </motion.article>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Comments Section */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <CommentList postId={post.id} />
          </div>
        </section>
      </div>
    </>
  );
};
