import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BarChart3, ChevronDown, ChevronUp, Search, HelpCircle } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { getPageSEO } from '../lib/seo-config';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { useNavigate } from 'react-router-dom';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

export const FAQPage: React.FC = () => {
  const navigate = useNavigate();
  const seoConfig = getPageSEO('faq');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState('all');

  const faqData: FAQItem[] = [
    {
      id: '1',
      question: 'How does AppReview.Today analyze app reviews?',
      answer: 'Our platform uses advanced AI technology powered by Google Gemini API to scrape reviews from multiple sources (App Store, Google Play, Reddit) and analyze them for sentiment, themes, and actionable insights. The AI processes natural language to identify patterns, user pain points, and feature requests.',
      category: 'general'
    },
    {
      id: '2',
      question: 'Which platforms do you support for review analysis?',
      answer: 'We currently support App Store (iOS), Google Play Store (Android), and Reddit discussions. Our platform can analyze reviews and user feedback from these sources to provide comprehensive insights about your app.',
      category: 'general'
    },
    {
      id: '3',
      question: 'How accurate is the sentiment analysis?',
      answer: 'Our AI-powered sentiment analysis achieves high accuracy by using Google\'s advanced Gemini models. The system is trained to understand context, sarcasm, and nuanced feedback. However, we recommend reviewing the generated insights as AI analysis should complement, not replace, human judgment.',
      category: 'technical'
    },
    {
      id: '4',
      question: 'What is included in the free plan?',
      answer: 'The free plan includes 1 comprehensive analysis report. This allows you to test our platform and see the quality of insights we provide. The report includes sentiment analysis, theme extraction, and actionable recommendations.',
      category: 'pricing'
    },
    {
      id: '5',
      question: 'How long does it take to generate a report?',
      answer: 'Report generation typically takes 2-5 minutes depending on the number of reviews being analyzed. Our system processes reviews in batches and provides real-time progress updates. You\'ll receive an email notification when your report is ready.',
      category: 'technical'
    },
    {
      id: '6',
      question: 'Can I export the analysis results?',
      answer: 'Yes! All reports can be exported as PDF files for easy sharing with your team. The PDF includes all insights, charts, and recommendations in a professional format suitable for presentations and documentation.',
      category: 'features'
    },
    {
      id: '7',
      question: 'Do you store our app data or reviews?',
      answer: 'We prioritize your privacy and security. Reviews are processed for analysis but not permanently stored on our servers. Generated reports and insights are stored securely and only accessible to your account. We never share your data with third parties.',
      category: 'privacy'
    },
    {
      id: '8',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, MasterCard, American Express) through our secure Stripe integration. All payments are processed securely and we don\'t store your payment information.',
      category: 'pricing'
    },
    {
      id: '9',
      question: 'Can I analyze competitor apps?',
      answer: 'Yes! You can analyze any publicly available app on the App Store or Google Play Store. This is perfect for competitive analysis and understanding market trends in your app category.',
      category: 'features'
    },
    {
      id: '10',
      question: 'Is there an API available?',
      answer: 'We\'re currently developing API access for enterprise customers. If you\'re interested in API integration, please contact us to discuss your specific needs and get early access.',
      category: 'technical'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'general', name: 'General' },
    { id: 'technical', name: 'Technical' },
    { id: 'pricing', name: 'Pricing' },
    { id: 'features', name: 'Features' },
    { id: 'privacy', name: 'Privacy & Security' }
  ];

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <>
      <SEOHead 
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        url="https://appreview.today/faq"
        type={seoConfig.type}
        structuredData={seoConfig.structuredData}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white cursor-pointer" onClick={() => navigate('/')}>
                AppReview.Today
              </span>
            </motion.div>
            <Button onClick={() => navigate('/')}>
              Back to Home
            </Button>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <HelpCircle className="h-16 w-16 text-blue-400 mx-auto mb-6" />
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                Frequently Asked <span className="text-blue-400">Questions</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8">
                Find answers to common questions about AppReview.Today
              </p>
            </motion.div>
          </div>
        </section>

        {/* Search and Filters */}
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6">
            <div className="flex flex-col md:flex-row gap-4 mb-8">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search questions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id} className="bg-gray-800">
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </section>

        {/* FAQ Items */}
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6">
            <div className="space-y-4">
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="overflow-hidden">
                    <button
                      onClick={() => toggleExpanded(faq.id)}
                      className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                    >
                      <h3 className="text-lg font-semibold text-white pr-4">{faq.question}</h3>
                      {expandedItems.has(faq.id) ? (
                        <ChevronUp className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
                      )}
                    </button>
                    
                    <AnimatePresence>
                      {expandedItems.has(faq.id) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div className="px-6 pb-6 border-t border-white/10">
                            <p className="text-gray-300 leading-relaxed pt-4">{faq.answer}</p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                </motion.div>
              ))}
            </div>

            {filteredFAQs.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-400 text-lg">No questions found matching your search.</p>
              </div>
            )}
          </div>
        </section>

        {/* Contact CTA */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-white mb-4">Still have questions?</h2>
              <p className="text-gray-300 text-lg mb-8">
                Can't find what you're looking for? We're here to help!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" onClick={() => navigate('/contact')}>
                  Contact Support
                </Button>
                <Button variant="secondary" size="lg" onClick={() => navigate('/help')}>
                  Help Center
                </Button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};
