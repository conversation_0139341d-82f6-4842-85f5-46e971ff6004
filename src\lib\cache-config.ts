// Cache configuration for React Query and browser caching

export const CACHE_KEYS = {
  // User data
  USER_PROFILE: 'user-profile',
  USER_REPORTS: 'user-reports',
  USER_PAYMENT_HISTORY: 'user-payment-history',
  
  // Reports
  REPORT_DETAILS: 'report-details',
  REPORT_LIST: 'report-list',
  
  // App data
  APP_SEARCH: 'app-search',
  APP_DETAILS: 'app-details',
  
  // Static data
  PRICING_PLANS: 'pricing-plans',
  FAQ_DATA: 'faq-data'
} as const

export const CACHE_TIMES = {
  // Short cache for frequently changing data
  SHORT: 5 * 60 * 1000, // 5 minutes
  
  // Medium cache for semi-static data
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  
  // Long cache for static data
  LONG: 24 * 60 * 60 * 1000, // 24 hours
  
  // Very long cache for rarely changing data
  VERY_LONG: 7 * 24 * 60 * 60 * 1000 // 7 days
} as const

// React Query default options
export const queryClientConfig = {
  defaultOptions: {
    queries: {
      // Global defaults
      staleTime: CACHE_TIMES.MEDIUM,
      gcTime: CACHE_TIMES.LONG, // Previously cacheTime
      retry: (failureCount: number, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false
        }
        // Retry up to 3 times for other errors
        return failureCount < 3
      },
      retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchOnMount: true
    },
    mutations: {
      retry: 1,
      retryDelay: 1000
    }
  }
}

// Specific cache configurations for different data types
export const cacheConfigs = {
  // User data - short cache as it can change frequently
  userProfile: {
    staleTime: CACHE_TIMES.SHORT,
    gcTime: CACHE_TIMES.MEDIUM
  },
  
  // Reports - medium cache as they don't change once generated
  reports: {
    staleTime: CACHE_TIMES.MEDIUM,
    gcTime: CACHE_TIMES.LONG
  },
  
  // Static content - long cache
  staticContent: {
    staleTime: CACHE_TIMES.LONG,
    gcTime: CACHE_TIMES.VERY_LONG
  },
  
  // Search results - short cache as they can be dynamic
  search: {
    staleTime: CACHE_TIMES.SHORT,
    gcTime: CACHE_TIMES.MEDIUM
  }
}

// Browser cache utilities
export const browserCache = {
  // Set item with expiration
  setItem: (key: string, value: any, ttl: number = CACHE_TIMES.MEDIUM) => {
    try {
      const item = {
        value,
        expiry: Date.now() + ttl
      }
      localStorage.setItem(key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to set cache item:', error)
    }
  },

  // Get item with expiration check
  getItem: (key: string) => {
    try {
      const itemStr = localStorage.getItem(key)
      if (!itemStr) return null

      const item = JSON.parse(itemStr)
      if (Date.now() > item.expiry) {
        localStorage.removeItem(key)
        return null
      }
      
      return item.value
    } catch (error) {
      console.warn('Failed to get cache item:', error)
      return null
    }
  },

  // Remove item
  removeItem: (key: string) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove cache item:', error)
    }
  },

  // Clear expired items
  clearExpired: () => {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        const itemStr = localStorage.getItem(key)
        if (itemStr) {
          try {
            const item = JSON.parse(itemStr)
            if (item.expiry && Date.now() > item.expiry) {
              localStorage.removeItem(key)
            }
          } catch {
            // Invalid JSON, remove it
            localStorage.removeItem(key)
          }
        }
      })
    } catch (error) {
      console.warn('Failed to clear expired cache items:', error)
    }
  },

  // Clear all cache
  clearAll: () => {
    try {
      localStorage.clear()
    } catch (error) {
      console.warn('Failed to clear cache:', error)
    }
  }
}

// Service Worker cache strategies
export const swCacheStrategies = {
  // Cache first for static assets
  cacheFirst: [
    /\.(?:js|css|woff2?|png|jpg|jpeg|svg|ico)$/,
    /^https:\/\/fonts\.googleapis\.com/,
    /^https:\/\/fonts\.gstatic\.com/
  ],

  // Network first for API calls
  networkFirst: [
    /^https:\/\/.*\.supabase\.co\/rest/,
    /^https:\/\/.*\.supabase\.co\/auth/,
    /^https:\/\/api\.stripe\.com/
  ],

  // Stale while revalidate for pages
  staleWhileRevalidate: [
    /^https:\/\/appreview\.today/,
    /^http:\/\/localhost/
  ]
}

// Preload critical resources
export const criticalResources = {
  fonts: [
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
  ],
  
  images: [
    '/app-review-today.svg',
    '/app-review-today-192.png'
  ],
  
  scripts: [
    // Add critical third-party scripts here
  ]
}

// Cache invalidation utilities
export const cacheInvalidation = {
  // Invalidate user-related caches
  invalidateUserCache: () => {
    browserCache.removeItem(CACHE_KEYS.USER_PROFILE)
    browserCache.removeItem(CACHE_KEYS.USER_REPORTS)
    browserCache.removeItem(CACHE_KEYS.USER_PAYMENT_HISTORY)
  },

  // Invalidate report caches
  invalidateReportCache: (reportId?: string) => {
    if (reportId) {
      browserCache.removeItem(`${CACHE_KEYS.REPORT_DETAILS}-${reportId}`)
    } else {
      browserCache.removeItem(CACHE_KEYS.REPORT_LIST)
    }
  },

  // Invalidate all caches
  invalidateAll: () => {
    browserCache.clearAll()
  }
}

// Initialize cache cleanup on app start
export const initializeCacheCleanup = () => {
  // Clear expired items on app start
  browserCache.clearExpired()

  // Set up periodic cleanup (every hour)
  setInterval(() => {
    browserCache.clearExpired()
  }, 60 * 60 * 1000)
}
