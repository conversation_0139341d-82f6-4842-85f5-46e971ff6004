import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Target, Users, Zap, Award, Globe, Heart, Code } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { getPageSEO } from '../lib/seo-config';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { useNavigate } from 'react-router-dom';

export const AboutPage: React.FC = () => {
  const navigate = useNavigate();
  const seoConfig = getPageSEO('about');

  const teamValues = [
    {
      icon: Target,
      title: 'Mission-Driven',
      description: 'We believe every app deserves to understand its users better and create meaningful experiences.'
    },
    {
      icon: Users,
      title: 'User-Centric',
      description: 'Our platform is built by developers, for developers, with real-world needs in mind.'
    },
    {
      icon: Zap,
      title: 'Innovation First',
      description: 'We leverage cutting-edge AI technology to transform how teams analyze user feedback.'
    },
    {
      icon: Heart,
      title: 'Community Focused',
      description: 'We are committed to supporting the global app development community with accessible tools.'
    }
  ];

  const milestones = [
    {
      year: '2024',
      title: 'Platform Launch',
      description: 'Launched AppReview.Today with multi-platform review analysis capabilities.'
    },
    {
      year: '2024',
      title: 'AI Integration',
      description: 'Integrated Google Gemini API for advanced sentiment analysis and insight generation.'
    },
    {
      year: '2025',
      title: 'Scale & Growth',
      description: 'Expanding features and serving developers worldwide with actionable review insights.'
    }
  ];

  return (
    <>
      <SEOHead 
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        url="https://appreview.today/about"
        type={seoConfig.type}
        structuredData={seoConfig.structuredData}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold text-white cursor-pointer" onClick={() => navigate('/')}>
                AppReview.Today
              </span>
            </motion.div>
            <Button onClick={() => navigate('/')}>
              Back to Home
            </Button>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-6xl font-bold text-white mb-6"
            >
              About <span className="text-blue-400">AppReview.Today</span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-300 mb-8 leading-relaxed"
            >
              We're on a mission to help app developers and product teams understand their users better 
              through intelligent review analysis and actionable insights.
            </motion.p>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6">
            <div className="grid md:grid-cols-2 gap-12">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <Card className="p-8 h-full">
                  <Target className="h-12 w-12 text-blue-400 mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-4">Our Mission</h2>
                  <p className="text-gray-300 leading-relaxed">
                    To democratize app review analysis by providing powerful, AI-driven insights that help 
                    developers of all sizes understand their users, improve their products, and build better 
                    mobile experiences.
                  </p>
                </Card>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
              >
                <Card className="p-8 h-full">
                  <Globe className="h-12 w-12 text-green-400 mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-4">Our Vision</h2>
                  <p className="text-gray-300 leading-relaxed">
                    A world where every app development team has access to deep user insights, enabling them 
                    to create products that truly resonate with their audience and drive meaningful engagement.
                  </p>
                </Card>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4 sm:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-white mb-4">Our Values</h2>
              <p className="text-gray-300 text-lg">The principles that guide everything we do</p>
            </motion.div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {teamValues.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="p-6 text-center h-full">
                    <value.icon className="h-10 w-10 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">{value.title}</h3>
                    <p className="text-gray-300 text-sm">{value.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Journey Timeline */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-white mb-4">Our Journey</h2>
              <p className="text-gray-300 text-lg">Key milestones in our mission to transform app review analysis</p>
            </motion.div>
            
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-6"
                >
                  <div className="flex-shrink-0 w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{milestone.year}</span>
                  </div>
                  <Card className="flex-1 p-6">
                    <h3 className="text-xl font-semibold text-white mb-2">{milestone.title}</h3>
                    <p className="text-gray-300">{milestone.description}</p>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Technology Stack */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <Code className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h2 className="text-3xl font-bold text-white mb-4">Built with Modern Technology</h2>
              <p className="text-gray-300 text-lg mb-8">
                Our platform leverages cutting-edge technologies to deliver fast, reliable, and intelligent 
                review analysis capabilities.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                {['React', 'TypeScript', 'Supabase', 'Google Gemini AI', 'Vercel', 'Tailwind CSS'].map((tech) => (
                  <span 
                    key={tech}
                    className="px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
              <p className="text-gray-300 text-lg mb-8">
                Join thousands of developers who trust AppReview.Today for their app review analysis needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" onClick={() => navigate('/')}>
                  Start Free Analysis
                </Button>
                <Button variant="secondary" size="lg" onClick={() => navigate('/contact')}>
                  Contact Us
                </Button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};
