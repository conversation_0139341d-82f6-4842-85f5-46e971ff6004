import React from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, CreditCard, Truck, Star, TrendingUp, CheckCircle, ArrowRight, Target } from 'lucide-react';
import { SEOHead } from '../components/SEOHead';
import { LazyImage } from '../components/LazyImage';
import { Footer } from '../components/Footer';

export const EcommerceAppsPage: React.FC = () => {
  const features = [
    {
      icon: <ShoppingCart className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Shopping Experience Analysis",
      description: "Analyze customer feedback on product discovery, search functionality, and overall shopping journey."
    },
    {
      icon: <CreditCard className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Checkout & Payment Insights",
      description: "Understand payment flow issues, checkout abandonment reasons, and payment method preferences."
    },
    {
      icon: <Truck className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Delivery & Service Feedback",
      description: "Monitor customer satisfaction with shipping, delivery times, customer service, and return processes."
    },
    {
      icon: <Star className="w-8 h-8 text-[#2DD4BF]" />,
      title: "Product & Review Analysis",
      description: "Analyze product feedback, review quality, and customer satisfaction across different categories."
    }
  ];

  const ecommerceTypes = [
    {
      title: "Fashion & Apparel",
      description: "Size accuracy, style preferences, and return experience optimization",
      insights: ["Size guide effectiveness", "Style recommendation accuracy", "Return process feedback", "Material quality mentions"],
      metrics: "67% reduction in returns"
    },
    {
      title: "Electronics & Tech",
      description: "Product specifications, compatibility, and technical support analysis",
      insights: ["Specification accuracy", "Compatibility issues", "Technical support quality", "Warranty experience"],
      metrics: "85% better product satisfaction"
    },
    {
      title: "Home & Garden",
      description: "Product quality, assembly instructions, and delivery experience",
      insights: ["Assembly difficulty feedback", "Product durability mentions", "Delivery condition analysis", "Size expectations"],
      metrics: "72% improved delivery satisfaction"
    },
    {
      title: "Beauty & Health",
      description: "Product effectiveness, ingredient concerns, and skin compatibility",
      insights: ["Ingredient sensitivity feedback", "Product effectiveness reviews", "Packaging quality", "Skin type compatibility"],
      metrics: "58% better product matching"
    }
  ];

  const challenges = [
    {
      challenge: "Cart Abandonment",
      solution: "Identify specific checkout friction points from customer feedback",
      impact: "35% reduction in cart abandonment"
    },
    {
      challenge: "Return Rates",
      solution: "Analyze return reasons to improve product descriptions and sizing",
      impact: "45% decrease in return rates"
    },
    {
      challenge: "Customer Service",
      solution: "Monitor service quality feedback to improve support processes",
      impact: "80% improvement in service ratings"
    },
    {
      challenge: "Product Discovery",
      solution: "Optimize search and recommendation based on user feedback",
      impact: "60% better product findability"
    }
  ];

  const metrics = [
    { value: "200M+", label: "E-commerce Reviews Analyzed", description: "Comprehensive retail industry dataset" },
    { value: "93%", label: "Purchase Intent Accuracy", description: "E-commerce specific sentiment models" },
    { value: "55%", label: "Faster Issue Resolution", description: "Real-time customer feedback monitoring" },
    { value: "38%", label: "Higher Conversion Rates", description: "Data-driven shopping experience optimization" }
  ];

  return (
    <>
      <SEOHead 
        title="E-commerce Apps Analytics - AppReview.Today | Customer Experience Optimization"
        description="Optimize your e-commerce app with customer feedback analysis. Improve shopping experience, reduce cart abandonment, boost conversions and customer satisfaction."
        canonical="/ecommerce-apps"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "E-commerce Apps Analytics - AppReview.Today",
          "description": "Customer experience optimization platform for e-commerce and retail mobile apps",
          "audience": {
            "@type": "Audience",
            "audienceType": "E-commerce Developers"
          },
          "about": {
            "@type": "Thing",
            "name": "E-commerce Analytics"
          }
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-[#0A1128] via-[#0F1B3C] to-[#0A1128]">
        {/* Header */}
        <header className="border-b border-white/10 backdrop-blur-sm sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img src="/app-review-today.svg" alt="AppReview.Today" className="h-8 w-8" />
                <span className="text-xl font-bold text-white">AppReview.Today</span>
              </div>
              <button
                onClick={() => window.location.href = '/'}
                className="px-4 py-2 text-white bg-white/10 hover:bg-white/20 font-medium transition-colors rounded-lg backdrop-blur-sm border border-white/20"
              >
                Back to Home
              </button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="flex justify-center mb-6">
                <ShoppingCart className="w-16 h-16 text-[#2DD4BF]" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
                Optimize Your <span className="text-[#2DD4BF]">E-commerce App</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-white/70">
                Transform customer feedback into better shopping experiences. Reduce cart abandonment,
                improve conversions, and build customer loyalty.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] hover:bg-[#14B8A6] text-[#0A1128] px-8 py-3 rounded-lg font-semibold transition-colors">
                  Analyze Customer Feedback
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View E-commerce Case Studies
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* E-commerce Features */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                E-commerce Focused Analytics
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Specialized insights for online retail and e-commerce mobile applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-white/70">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* E-commerce Categories */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Tailored for Every E-commerce Category
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Specialized analytics for different types of online retail businesses
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {ecommerceTypes.map((type, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl"
                >
                  <h3 className="text-xl font-bold text-white mb-3">
                    {type.title}
                  </h3>
                  <p className="text-white/70 mb-6">
                    {type.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {type.insights.map((insight, insightIndex) => (
                      <div key={insightIndex} className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-[#2DD4BF] flex-shrink-0" />
                        <span className="text-white/70">{insight}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-[#2DD4BF]/20 p-4 rounded-lg border border-[#2DD4BF]/30">
                    <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold">
                      <TrendingUp className="w-4 h-4" />
                      {type.metrics}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* E-commerce Challenges */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Solve E-commerce Challenges
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Address the most common issues facing e-commerce app developers
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {challenges.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 shadow-lg"
                >
                  <div className="flex items-start gap-4 mb-4">
                    <Target className="w-6 h-6 text-red-400 flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-lg font-bold text-white mb-2">
                        Challenge: {item.challenge}
                      </h3>
                      <p className="text-white/70 mb-4">
                        {item.solution}
                      </p>
                      <div className="bg-[#2DD4BF]/20 p-3 rounded-lg border border-[#2DD4BF]/30">
                        <div className="flex items-center gap-2 text-[#2DD4BF] font-semibold text-sm">
                          <TrendingUp className="w-4 h-4" />
                          {item.impact}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* E-commerce Metrics */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/5">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Proven Results for E-commerce Apps
              </h2>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                See how e-commerce businesses are improving customer satisfaction and sales
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm border border-white/20 p-8 rounded-2xl text-center"
                >
                  <div className="text-4xl font-bold text-[#2DD4BF] mb-2">
                    {metric.value}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">
                    {metric.label}
                  </div>
                  <div className="text-white/70 text-sm">
                    {metric.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-[#2DD4BF]/10 to-[#14B8A6]/10 border-[#2DD4BF]/20 bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Boost Your E-commerce Success?
              </h2>
              <p className="text-xl text-white/70 mb-8 max-w-2xl mx-auto">
                Join successful e-commerce businesses that are improving customer experience with data-driven insights.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-[#2DD4BF] text-[#0A1128] hover:bg-[#14B8A6] px-8 py-3 rounded-lg font-semibold transition-colors flex items-center gap-2">
                  Start E-commerce Analysis
                  <ArrowRight className="w-4 h-4" />
                </button>
                <button className="border border-white/20 text-white hover:bg-white/10 px-8 py-3 rounded-lg font-semibold transition-colors backdrop-blur-sm">
                  View E-commerce Demo
                </button>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
      
      <Footer />
    </>
  );
};
