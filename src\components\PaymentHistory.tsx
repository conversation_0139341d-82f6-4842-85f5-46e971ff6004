import React, { useState, useEffect } from 'react'
import { CheckCircle, XCircle, Clock, AlertCircle, DollarSign } from 'lucide-react'
import { getUserPaymentLogs } from '../lib/database'
import { useAuthStore } from '../stores/authStore'
import { LoadingSpinner } from './LoadingSpinner'

export const PaymentHistory: React.FC = () => {
  const [paymentLogs, setPaymentLogs] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { user } = useAuthStore()

  useEffect(() => {
    if (user) {
      loadPaymentHistory()
    }
  }, [user])

  const loadPaymentHistory = async () => {
    if (!user) return
    
    setIsLoading(true)
    try {
      const logs = await getUserPaymentLogs(user.id, 20)
      setPaymentLogs(logs || [])
    } catch (error) {
      console.debug('Payment history load failed (this is normal for new users):', error)
      setPaymentLogs([])
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'succeeded':
        return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400" />
      case 'created':
        return <AlertCircle className="w-4 h-4 text-blue-400" />
      case 'canceled':
        return <XCircle className="w-4 h-4 text-gray-400" />
      default:
        return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'succeeded':
        return 'Payment Successful'
      case 'failed':
        return 'Payment Failed'
      case 'processing':
        return 'Processing'
      case 'created':
        return 'Payment Created'
      case 'canceled':
        return 'Payment Canceled'
      default:
        return status
    }
  }

  const formatAmount = (amount: number, currency: string = 'usd') => {
    if (!amount) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100) // Convert from cents
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="bg-[#1a2332] rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <DollarSign className="w-5 h-5 text-[#2DD4BF]" />
          Payment History
        </h3>
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="bg-[#1a2332] rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <DollarSign className="w-5 h-5 text-[#2DD4BF]" />
          Payment History
        </h3>
      </div>

      {paymentLogs.length === 0 ? (
        <div className="text-center py-8">
          <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-400">No payment records yet</p>
          <p className="text-sm text-gray-500 mt-1">Your payment history will appear here after making purchases</p>
        </div>
      ) : (
        <div className="space-y-3">
          {paymentLogs.map((log: any, index: number) => (
            <div key={log.id || index} className="bg-[#0A1128] rounded-lg p-4 border border-[#2DD4BF]/20">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  {getStatusIcon(log.payment_status)}
                  <div>
                    <p className="text-white font-medium">{log.plan_name || 'Unknown Plan'}</p>
                    <p className="text-sm text-gray-400">{getStatusText(log.payment_status)}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">{formatAmount(log.amount, log.currency)}</p>
                  <p className="text-xs text-gray-500">{formatDate(log.created_at)}</p>
                </div>
              </div>
              
              {log.reports_purchased && (
                <div className="flex items-center gap-1 text-sm text-[#2DD4BF]">
                  <span>+{log.reports_purchased} analysis reports</span>
                </div>
              )}
              
              {log.error_message && (
                <div className="mt-2 text-sm text-red-400 bg-red-400/10 p-2 rounded">
                  {log.error_message}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
} 